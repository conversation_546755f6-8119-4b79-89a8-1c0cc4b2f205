{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title mb-0">Modifier l'ingrédient</h2>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="name" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ ingredient.name }}" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="category_id" class="form-label">Catégorie</label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Sélectionner une catégorie</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}" 
                                        {% if category.id == ingredient.category_id %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Prix</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="price" name="price" 
                                               step="0.01" min="0" value="{{ ingredient.price }}" required>
                                        <span class="input-group-text">€</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="stock" class="form-label">Stock actuel</label>
                                    <input type="number" class="form-control" id="stock" name="stock" 
                                           step="0.01" min="0" value="{{ ingredient.stock }}" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="unit" class="form-label">Unité</label>
                                    <select class="form-select" id="unit" name="unit" required>
                                        <option value="kg" {% if ingredient.unit == 'kg' %}selected{% endif %}>
                                            Kilogramme (kg)
                                        </option>
                                        <option value="g" {% if ingredient.unit == 'g' %}selected{% endif %}>
                                            Gramme (g)
                                        </option>
                                        <option value="l" {% if ingredient.unit == 'l' %}selected{% endif %}>
                                            Litre (l)
                                        </option>
                                        <option value="ml" {% if ingredient.unit == 'ml' %}selected{% endif %}>
                                            Millilitre (ml)
                                        </option>
                                        <option value="unit" {% if ingredient.unit == 'unit' %}selected{% endif %}>
                                            Unité
                                        </option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="min_stock" class="form-label">Stock minimum</label>
                                    <input type="number" class="form-control" id="min_stock" name="min_stock" 
                                           step="0.01" min="0" value="{{ ingredient.min_stock }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="expiry_date" class="form-label">Date d'expiration</label>
                            <input type="date" class="form-control" id="expiry_date" name="expiry_date"
                                   value="{{ ingredient.expiry_date.strftime('%Y-%m-%d') if ingredient.expiry_date else '' }}">
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('inventory.ingredients') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 