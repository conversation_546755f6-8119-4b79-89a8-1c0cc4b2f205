{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h2>{{ product.name }}</h2>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('inventory.edit_product', product_id=product.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit"></i> Modifier
                </a>
                <button type="button" class="btn btn-danger" onclick="deleteProduct({{ product.id }})">
                    <i class="fas fa-trash"></i> Supprimer
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Informations</h5>
                    <dl class="row mb-0">
                        <dt class="col-sm-4">Catégorie</dt>
                        <dd class="col-sm-8">{{ product.category.name if product.category else 'Non catégorisé' }}</dd>
                        
                        <dt class="col-sm-4">Prix</dt>
                        <dd class="col-sm-8">{{ product.price|round(2) }} €</dd>
                        
                        <dt class="col-sm-4">Stock</dt>
                        <dd class="col-sm-8">
                            <span class="badge bg-{{ 'success' if product.stock > 5 else 'warning' if product.stock > 0 else 'danger' }}">
                                {{ product.stock }} {{ product.unit }}
                            </span>
                        </dd>
                        
                        <dt class="col-sm-4">Description</dt>
                        <dd class="col-sm-8">{{ product.description or 'Aucune description' }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Mouvement de stock</h5>
                    <form id="stockMovementForm">
                        <input type="hidden" id="productId" value="{{ product.id }}">
                        <div class="mb-3">
                            <label class="form-label">Type</label>
                            <select class="form-select" id="movementType" required>
                                <option value="in">Entrée</option>
                                <option value="out">Sortie</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Quantité</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="quantity" step="0.01" min="0" required>
                                <span class="input-group-text">{{ product.unit }}</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" id="description" rows="2" required></textarea>
                        </div>
                        <button type="button" class="btn btn-primary" onclick="saveStockMovement()">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title">Historique des mouvements</h5>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Quantité</th>
                            <th>Description</th>
                            <th>Utilisateur</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for movement in stock_movements %}
                        <tr>
                            <td>{{ movement.date.strftime('%d/%m/%Y %H:%M') }}</td>
                            <td>
                                <span class="badge bg-{{ 'success' if movement.type == 'in' else 'danger' }}">
                                    {{ 'Entrée' if movement.type == 'in' else 'Sortie' }}
                                </span>
                            </td>
                            <td>{{ movement.quantity }} {{ product.unit }}</td>
                            <td>{{ movement.description }}</td>
                            <td>{{ movement.user.username }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function deleteProduct(productId) {
        if (confirm('Voulez-vous vraiment supprimer ce produit ?')) {
            fetch(`/inventory/delete_product/${productId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Produit supprimé avec succès');
                    window.location.href = '/inventory/products';
                } else {
                    showNotification(data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Erreur lors de la suppression du produit', 'danger');
            });
        }
    }

    function saveStockMovement() {
        const productId = document.getElementById('productId').value;
        const type = document.getElementById('movementType').value;
        const quantity = parseFloat(document.getElementById('quantity').value);
        const description = document.getElementById('description').value;

        fetch(`/inventory/stock_movement/${productId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                type: type,
                quantity: quantity,
                description: description
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Mouvement de stock enregistré avec succès');
                window.location.reload();
            } else {
                showNotification(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Erreur lors de l\'enregistrement du mouvement', 'danger');
        });
    }
</script>
{% endblock %} 