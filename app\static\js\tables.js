/* ===== MODERN TABLE MANAGEMENT JAVASCRIPT ===== */

class TableManager {
    constructor() {
        this.currentView = 'grid';
        this.selectedTable = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateStats();
        
        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
        
        console.log('Table Manager initialized');
    }

    setupEventListeners() {
        // View toggle buttons
        document.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.addEventListener('click', () => this.toggleView(btn.dataset.view));
        });

        // Modal close on background click
        document.getElementById('tableModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.closeTableModal();
            }
        });

        document.getElementById('addTableModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.closeAddTableModal();
            }
        });
    }

    toggleView(view) {
        this.currentView = view;
        
        // Update active button
        document.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.view === view);
        });
        
        // Toggle views
        const gridView = document.getElementById('gridView');
        const listView = document.getElementById('listView');
        
        if (view === 'list') {
            gridView.classList.add('hidden');
            listView.classList.remove('hidden');
        } else {
            gridView.classList.remove('hidden');
            listView.classList.add('hidden');
        }
    }

    addNewTable() {
        const modal = document.getElementById('addTableModal');
        modal.classList.add('open');
        
        // Reset form
        document.getElementById('addTableForm').reset();
        
        // Focus on first input
        setTimeout(() => {
            document.getElementById('tableNumber').focus();
        }, 300);
    }

    closeAddTableModal() {
        const modal = document.getElementById('addTableModal');
        modal.classList.remove('open');
    }

    async submitAddTable() {
        const form = document.getElementById('addTableForm');
        const formData = new FormData(form);
        const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
        
        try {
            showLoading();
            
            const response = await fetch('/pos/add_table', {
                method: 'POST',
                headers: {
                    'X-CSRF-Token': csrfToken
                },
                body: formData
            });

            const data = await response.json();
            
            if (data.success) {
                showNotification('Table créée avec succès !', 'success');
                this.closeAddTableModal();
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showNotification(data.message || 'Erreur lors de la création', 'error');
            }
        } catch (error) {
            console.error('Error adding table:', error);
            showNotification('Erreur de connexion', 'error');
        } finally {
            hideLoading();
        }
    }

    openTableModal(id, number, status, capacity) {
        this.selectedTable = { id, number, status, capacity };
        
        const modal = document.getElementById('tableModal');
        const title = document.getElementById('modalTableTitle');
        const content = document.getElementById('modalContent');
        
        title.textContent = `Table #${number}`;
        
        // Generate modal content based on table status
        content.innerHTML = this.generateTableModalContent(status, capacity);
        
        modal.classList.add('open');
        
        // Reinitialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    generateTableModalContent(status, capacity) {
        const statusConfig = {
            free: {
                color: 'accent',
                icon: 'check-circle',
                title: 'Table disponible',
                actions: ['Réserver', 'Assigner clients', 'Modifier']
            },
            occupied: {
                color: 'primary',
                icon: 'users',
                title: 'Table occupée',
                actions: ['Voir commande', 'Ajouter items', 'Encaisser', 'Libérer']
            },
            reserved: {
                color: 'yellow',
                icon: 'clock',
                title: 'Table réservée',
                actions: ['Voir réservation', 'Confirmer arrivée', 'Annuler']
            }
        };

        const config = statusConfig[status];
        
        return `
            <div class="space-y-6">
                <!-- Status Card -->
                <div class="glass-card p-4 border border-${config.color}-500/30">
                    <div class="flex items-center gap-3 mb-3">
                        <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-${config.color}-500 to-${config.color}-600 flex items-center justify-center">
                            <i data-lucide="${config.icon}" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-neutral-100">${config.title}</h4>
                            <p class="text-sm text-neutral-400">${capacity} places</p>
                        </div>
                    </div>
                    
                    ${status === 'occupied' ? `
                        <div class="text-sm text-neutral-300">
                            <div class="flex justify-between mb-1">
                                <span>Temps d'occupation:</span>
                                <span class="font-mono text-${config.color}-400">1h 23min</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Montant actuel:</span>
                                <span class="font-mono text-${config.color}-400">45.80€</span>
                            </div>
                        </div>
                    ` : ''}
                    
                    ${status === 'reserved' ? `
                        <div class="text-sm text-neutral-300">
                            <div class="flex justify-between mb-1">
                                <span>Réservation:</span>
                                <span class="font-mono text-${config.color}-400">19:30</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Client:</span>
                                <span class="text-${config.color}-400">M. Dupont</span>
                            </div>
                        </div>
                    ` : ''}
                </div>

                <!-- Actions -->
                <div class="space-y-3">
                    <h5 class="text-sm font-medium text-neutral-300">Actions disponibles</h5>
                    <div class="grid grid-cols-2 gap-3">
                        ${config.actions.map(action => `
                            <button class="btn-ghost-modern justify-center py-3" onclick="tableAction('${action.toLowerCase().replace(' ', '_')}')">
                                ${this.getActionIcon(action)}
                                ${action}
                            </button>
                        `).join('')}
                    </div>
                </div>

                <!-- Table Management -->
                <div class="border-t border-neutral-700 pt-4">
                    <h5 class="text-sm font-medium text-neutral-300 mb-3">Gestion de la table</h5>
                    <div class="flex gap-3">
                        <button onclick="editTable(${this.selectedTable?.id})" class="btn-ghost-modern flex-1 justify-center">
                            <i data-lucide="edit" class="w-4 h-4"></i>
                            Modifier
                        </button>
                        <button onclick="deleteTable(${this.selectedTable?.id})" class="btn-ghost-modern flex-1 justify-center text-red-400 hover:text-red-300">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                            Supprimer
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    getActionIcon(action) {
        const icons = {
            'Réserver': '<i data-lucide="calendar" class="w-4 h-4"></i>',
            'Assigner clients': '<i data-lucide="user-plus" class="w-4 h-4"></i>',
            'Modifier': '<i data-lucide="edit" class="w-4 h-4"></i>',
            'Voir commande': '<i data-lucide="receipt" class="w-4 h-4"></i>',
            'Ajouter items': '<i data-lucide="plus-circle" class="w-4 h-4"></i>',
            'Encaisser': '<i data-lucide="credit-card" class="w-4 h-4"></i>',
            'Libérer': '<i data-lucide="log-out" class="w-4 h-4"></i>',
            'Voir réservation': '<i data-lucide="eye" class="w-4 h-4"></i>',
            'Confirmer arrivée': '<i data-lucide="check" class="w-4 h-4"></i>',
            'Annuler': '<i data-lucide="x" class="w-4 h-4"></i>'
        };
        return icons[action] || '<i data-lucide="circle" class="w-4 h-4"></i>';
    }

    closeTableModal() {
        const modal = document.getElementById('tableModal');
        modal.classList.remove('open');
        this.selectedTable = null;
    }

    async editTable(id) {
        showNotification('Fonction d\'édition en développement', 'info');
    }

    async deleteTable(id) {
        if (!confirm('Êtes-vous sûr de vouloir supprimer cette table ?')) {
            return;
        }

        try {
            showLoading();
            
            const response = await fetch(`/pos/delete_table/${id}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const data = await response.json();
            
            if (data.success) {
                showNotification('Table supprimée avec succès', 'success');
                this.closeTableModal();
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showNotification(data.message || 'Erreur lors de la suppression', 'error');
            }
        } catch (error) {
            console.error('Error deleting table:', error);
            showNotification('Erreur de connexion', 'error');
        } finally {
            hideLoading();
        }
    }

    updateStats() {
        // This would typically fetch real-time data from the server
        // For now, we'll use the values from the template
        console.log('Stats updated');
    }
}

// Table action handler
function tableAction(action) {
    switch(action) {
        case 'réserver':
            showNotification('Ouverture du système de réservation...', 'info');
            break;
        case 'assigner_clients':
            showNotification('Attribution de clients à la table...', 'info');
            break;
        case 'voir_commande':
            window.location.href = '/pos/new_sale';
            break;
        case 'ajouter_items':
            window.location.href = '/pos/new_sale';
            break;
        case 'encaisser':
            showNotification('Ouverture du module de paiement...', 'info');
            break;
        case 'libérer':
            showNotification('Libération de la table...', 'success');
            break;
        default:
            showNotification(`Action "${action}" en développement`, 'info');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.tableManager = new TableManager();
});

// Global functions for template compatibility
window.addNewTable = () => window.tableManager?.addNewTable();
window.closeAddTableModal = () => window.tableManager?.closeAddTableModal();
window.submitAddTable = () => window.tableManager?.submitAddTable();
window.openTableModal = (id, number, status, capacity) => window.tableManager?.openTableModal(id, number, status, capacity);
window.closeTableModal = () => window.tableManager?.closeTableModal();
window.editTable = (id) => window.tableManager?.editTable(id);
window.deleteTable = (id) => window.tableManager?.deleteTable(id);
window.toggleView = (view) => window.tableManager?.toggleView(view);
