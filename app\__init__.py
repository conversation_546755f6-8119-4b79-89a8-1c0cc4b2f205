from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager
from flask_migrate import Migrate
from flask_mail import Mail
from flask_bcrypt import Bcrypt
from flask_caching import Cache
from flask_session import Session
from flask_principal import Principal
from flask_babel import Babel
from flask_wtf.csrf import CSRFProtect
import os

# Initialisation des extensions
db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()
mail = Mail()
bcrypt = Bcrypt()
cache = Cache()
session = Session()
principal = Principal()
babel = Babel()
csrf = CSRFProtect()

def create_app(config_class=None):
    app = Flask(__name__)
    
    # Configuration de base
    if config_class:
        app.config.from_object(config_class)
    else:
        app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or 'dev-key-please-change'
        app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL') or 'sqlite:///' + os.path.join(app.instance_path, 'app.db')
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        app.config['SESSION_TYPE'] = 'filesystem'
        app.config['CACHE_TYPE'] = 'simple'
        app.config['WTF_CSRF_ENABLED'] = True
        app.config['WTF_CSRF_SECRET_KEY'] = os.environ.get('WTF_CSRF_SECRET_KEY') or 'csrf-secret-key'
    
    # Configuration de la session
    app.config['SESSION_TYPE'] = 'filesystem'
    
    # Configuration du cache
    app.config['CACHE_TYPE'] = 'simple'
    
    # Configuration de l'email
    app.config['MAIL_SERVER'] = os.environ.get('MAIL_SERVER')
    app.config['MAIL_PORT'] = int(os.environ.get('MAIL_PORT') or 25)
    app.config['MAIL_USE_TLS'] = os.environ.get('MAIL_USE_TLS') is not None
    app.config['MAIL_USERNAME'] = os.environ.get('MAIL_USERNAME')
    app.config['MAIL_PASSWORD'] = os.environ.get('MAIL_PASSWORD')
    
    # Initialisation des extensions avec l'application
    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)
    mail.init_app(app)
    bcrypt.init_app(app)
    cache.init_app(app)
    session.init_app(app)
    principal.init_app(app)
    csrf.init_app(app)
    babel.init_app(app)
    
    # Configuration du login manager
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'
    login_manager.login_message_category = 'info'
    
    # Enregistrement des blueprints
    from app.auth import auth_bp
    from app.main import main_bp
    from app.pos import pos_bp
    from app.inventory import inventory_bp
    from app.reports import reports_bp
    from app.settings import settings_bp
    
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(main_bp)
    app.register_blueprint(pos_bp, url_prefix='/pos')
    app.register_blueprint(inventory_bp, url_prefix='/inventory')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(settings_bp, url_prefix='/settings')
    
    # Ajout du filtre format_currency
    @app.template_filter('format_currency')
    def format_currency(value):
        try:
            return f"{float(value):.2f} €"
        except (ValueError, TypeError):
            return "0.00 €"
    
    # Création des tables de la base de données
    with app.app_context():
        db.create_all()
    
    return app 