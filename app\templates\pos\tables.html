{% extends "base.html" %}

{% block title %}Gestion des Tables - FuturePOS{% endblock %}

{% block content %}
<!-- Modern Table Management Interface -->
<div class="px-6 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8 animate-fade-in-up">
        <div>
            <h1 class="text-3xl font-bold text-neutral-100 mb-2">
                Gestion des Tables
                <span class="text-primary-400">Restaurant</span>
            </h1>
            <p class="text-neutral-400">
                Vue d'ensemble des tables • {{ tables|length }} tables configurées
            </p>
        </div>
        <div class="flex items-center gap-4">
            <div class="glass-card px-4 py-2">
                <div class="flex items-center gap-2 text-sm">
                    <div class="w-2 h-2 bg-accent-400 rounded-full animate-pulse"></div>
                    <span class="text-neutral-300">Temps réel</span>
                </div>
            </div>
            <button onclick="addNewTable()" class="btn-primary-modern">
                <i data-lucide="plus" class="w-4 h-4"></i>
                Nouvelle table
            </button>
        </div>
    </div>

    <!-- Table Status Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8 stagger-children">
        <div class="glass-card p-6 hover-lift">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold font-mono text-accent-400" id="availableTables">
                        {{ tables|selectattr('status', 'equalto', 'free')|list|length }}
                    </div>
                    <div class="text-xs text-neutral-400">Disponibles</div>
                </div>
            </div>
            <h3 class="text-sm font-medium text-neutral-300">Tables libres</h3>
        </div>

        <div class="glass-card p-6 hover-lift">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
                    <i data-lucide="users" class="w-6 h-6 text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold font-mono text-primary-400" id="occupiedTables">
                        {{ tables|selectattr('status', 'equalto', 'occupied')|list|length }}
                    </div>
                    <div class="text-xs text-neutral-400">Occupées</div>
                </div>
            </div>
            <h3 class="text-sm font-medium text-neutral-300">En service</h3>
        </div>

        <div class="glass-card p-6 hover-lift">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center">
                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold font-mono text-yellow-400" id="reservedTables">
                        {{ tables|selectattr('status', 'equalto', 'reserved')|list|length }}
                    </div>
                    <div class="text-xs text-neutral-400">Réservées</div>
                </div>
            </div>
            <h3 class="text-sm font-medium text-neutral-300">Réservations</h3>
        </div>

        <div class="glass-card p-6 hover-lift">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-secondary-500 to-secondary-600 flex items-center justify-center">
                    <i data-lucide="euro" class="w-6 h-6 text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold font-mono text-secondary-400" id="totalRevenue">{{ daily_total|round(2) }}€</div>
                    <div class="text-xs text-neutral-400">Aujourd'hui</div>
                </div>
            </div>
            <h3 class="text-sm font-medium text-neutral-300">Chiffre d'affaires</h3>
        </div>
    </div>

    <!-- Restaurant Floor Plan -->
    <div class="glass-card p-6 animate-fade-in-up">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-neutral-100 flex items-center gap-2">
                <i data-lucide="layout" class="w-5 h-5 text-primary-400"></i>
                Plan de salle
            </h2>
            <div class="flex items-center gap-2">
                <button onclick="toggleView('grid')" class="view-toggle-btn active" data-view="grid">
                    <i data-lucide="grid-3x3" class="w-4 h-4"></i>
                </button>
                <button onclick="toggleView('list')" class="view-toggle-btn" data-view="list">
                    <i data-lucide="list" class="w-4 h-4"></i>
                </button>
            </div>
        </div>

        <!-- Grid View -->
        <div id="gridView" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {% for table in tables %}
            <div class="table-card-modern {% if table.status == 'free' %}table-free{% elif table.status == 'occupied' %}table-occupied{% else %}table-reserved{% endif %}"
                 data-table-id="{{ table.id }}"
                 onclick="openTableModal({{ table.id }}, '{{ table.number }}', '{{ table.status }}', {{ table.capacity }})">
                <div class="table-card-header">
                    <div class="table-number">{{ table.number }}</div>
                    <div class="table-status-indicator"></div>
                </div>
                <div class="table-card-body">
                    <div class="table-capacity">
                        <i data-lucide="users" class="w-4 h-4"></i>
                        {{ table.capacity }}
                    </div>
                    <div class="table-status-text">
                        {% if table.status == 'free' %}
                            Libre
                        {% elif table.status == 'occupied' %}
                            Occupée
                        {% else %}
                            Réservée
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- List View -->
        <div id="listView" class="hidden space-y-3">
            {% for table in tables %}
            <div class="flex items-center justify-between p-4 rounded-lg bg-neutral-800/50 border border-neutral-700 hover:border-primary-500/30 transition-colors">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 rounded-lg bg-gradient-to-br
                        {% if table.status == 'free' %}from-accent-500 to-accent-600{% elif table.status == 'occupied' %}from-primary-500 to-primary-600{% else %}from-yellow-500 to-yellow-600{% endif %}
                        flex items-center justify-center">
                        <span class="text-white font-bold">{{ table.number }}</span>
                    </div>
                    <div>
                        <div class="font-medium text-neutral-100">Table {{ table.number }}</div>
                        <div class="text-sm text-neutral-400">{{ table.capacity }} places</div>
                    </div>
                </div>
                <div class="flex items-center gap-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                        {% if table.status == 'free' %}bg-accent-500/20 text-accent-400{% elif table.status == 'occupied' %}bg-primary-500/20 text-primary-400{% else %}bg-yellow-500/20 text-yellow-400{% endif %}">
                        {% if table.status == 'free' %}
                            Libre
                        {% elif table.status == 'occupied' %}
                            Occupée
                        {% else %}
                            Réservée
                        {% endif %}
                    </span>
                    <div class="flex items-center gap-2">
                        <button onclick="editTable({{ table.id }})" class="quick-action-btn-modern">
                            <i data-lucide="edit" class="w-3 h-3"></i>
                        </button>
                        <button onclick="deleteTable({{ table.id }})" class="quick-action-btn-modern text-red-400 hover:text-red-300">
                            <i data-lucide="trash-2" class="w-3 h-3"></i>
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Modern Table Modal -->
<div id="tableModal" class="payment-modal-modern">
    <div class="payment-modal-content-modern max-w-2xl">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-neutral-100 flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
                    <i data-lucide="table" class="w-5 h-5 text-white"></i>
                </div>
                <span id="modalTableTitle">Table #1</span>
            </h3>
            <button onclick="closeTableModal()" class="quick-action-btn-modern">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
        </div>

        <div id="modalContent">
            <!-- Content will be populated dynamically -->
        </div>
    </div>
</div>

<!-- Add Table Modal -->
<div id="addTableModal" class="payment-modal-modern">
    <div class="payment-modal-content-modern">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-neutral-100 flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center">
                    <i data-lucide="plus-circle" class="w-5 h-5 text-white"></i>
                </div>
                Nouvelle table
            </h3>
            <button onclick="closeAddTableModal()" class="quick-action-btn-modern">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
        </div>

        <form id="addTableForm" class="space-y-6">
            <div>
                <label class="block text-sm font-medium text-neutral-300 mb-2">
                    <i data-lucide="hash" class="w-4 h-4 inline mr-2"></i>
                    Numéro de table
                </label>
                <input type="number"
                       name="tableNumber"
                       id="tableNumber"
                       class="input-modern"
                       placeholder="Ex: 12"
                       min="1"
                       max="999"
                       required>
            </div>

            <div>
                <label class="block text-sm font-medium text-neutral-300 mb-2">
                    <i data-lucide="users" class="w-4 h-4 inline mr-2"></i>
                    Capacité (nombre de places)
                </label>
                <input type="number"
                       name="capacity"
                       id="capacity"
                       class="input-modern"
                       placeholder="Ex: 4"
                       min="1"
                       max="20"
                       required>
            </div>

            <div>
                <label class="block text-sm font-medium text-neutral-300 mb-3">
                    <i data-lucide="map-pin" class="w-4 h-4 inline mr-2"></i>
                    Zone du restaurant
                </label>
                <div class="grid grid-cols-2 gap-3">
                    <label class="payment-method-option">
                        <input type="radio" name="zone" value="main" class="sr-only" checked>
                        <div class="payment-method-card">
                            <i data-lucide="home" class="w-6 h-6 mb-2"></i>
                            <span class="font-medium">Salle principale</span>
                        </div>
                    </label>
                    <label class="payment-method-option">
                        <input type="radio" name="zone" value="terrace" class="sr-only">
                        <div class="payment-method-card">
                            <i data-lucide="sun" class="w-6 h-6 mb-2"></i>
                            <span class="font-medium">Terrasse</span>
                        </div>
                    </label>
                </div>
            </div>
        </form>

        <div class="flex gap-3 mt-8">
            <button type="button"
                    onclick="closeAddTableModal()"
                    class="btn-ghost-modern flex-1 py-3">
                <i data-lucide="x" class="w-4 h-4"></i>
                Annuler
            </button>
            <button type="button"
                    onclick="submitAddTable()"
                    class="btn-primary-modern flex-1 py-3 text-lg font-semibold">
                <i data-lucide="check" class="w-4 h-4"></i>
                Créer la table
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/tables.js') }}"></script>
{% endblock %}