/* ===== MODERN KITCHEN INTERFACE JAVASCRIPT ===== */

class KitchenManager {
    constructor() {
        this.orders = {
            pending: [],
            cooking: [],
            ready: []
        };
        this.selectedOrder = null;
        this.refreshInterval = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadOrders();
        this.startAutoRefresh();
        
        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
        
        console.log('Kitchen Manager initialized');
    }

    setupEventListeners() {
        // Modal close on background click
        document.getElementById('orderModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.closeOrderModal();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeOrderModal();
            }
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                this.refreshOrders();
            }
        });
    }

    async loadOrders() {
        try {
            // Simulate loading orders - replace with actual API call
            this.orders = {
                pending: this.generateMockOrders('pending', 3),
                cooking: this.generateMockOrders('cooking', 2),
                ready: this.generateMockOrders('ready', 1)
            };
            
            this.updateDisplay();
        } catch (error) {
            console.error('Error loading orders:', error);
            showNotification('Erreur lors du chargement des commandes', 'error');
        }
    }

    generateMockOrders(status, count) {
        const orders = [];
        const items = ['Pizza Margherita', 'Burger Classic', 'Salade César', 'Pâtes Carbonara', 'Steak Frites'];
        const tables = [1, 2, 3, 4, 5, 6, 7, 8];
        
        for (let i = 0; i < count; i++) {
            const orderItems = [];
            const itemCount = Math.floor(Math.random() * 3) + 1;
            
            for (let j = 0; j < itemCount; j++) {
                orderItems.push({
                    name: items[Math.floor(Math.random() * items.length)],
                    quantity: Math.floor(Math.random() * 3) + 1,
                    notes: Math.random() > 0.7 ? 'Sans oignons' : null
                });
            }
            
            orders.push({
                id: Date.now() + i,
                table: tables[Math.floor(Math.random() * tables.length)],
                items: orderItems,
                time: new Date(Date.now() - Math.random() * 30 * 60 * 1000), // Random time in last 30 minutes
                status: status,
                priority: Math.random() > 0.8 ? 'high' : 'normal'
            });
        }
        
        return orders;
    }

    updateDisplay() {
        this.updateStats();
        this.updateOrderLists();
    }

    updateStats() {
        const totalPending = this.orders.pending.length;
        const totalCooking = this.orders.cooking.length;
        const totalReady = this.orders.ready.length;
        const totalActive = totalPending + totalCooking + totalReady;
        
        document.getElementById('activeOrders').textContent = totalActive;
        document.getElementById('pendingOrders').textContent = totalPending;
        document.getElementById('cookingOrders').textContent = totalCooking;
        document.getElementById('readyOrders').textContent = totalReady;
        
        // Calculate average preparation time
        const avgTime = this.calculateAverageTime();
        document.getElementById('avgTime').textContent = `${avgTime}min`;
        
        // Update counters
        document.getElementById('pendingCount').textContent = totalPending;
        document.getElementById('cookingCount').textContent = totalCooking;
        document.getElementById('readyCount').textContent = totalReady;
    }

    calculateAverageTime() {
        const allOrders = [...this.orders.cooking, ...this.orders.ready];
        if (allOrders.length === 0) return 0;
        
        const totalTime = allOrders.reduce((sum, order) => {
            const elapsed = (Date.now() - order.time.getTime()) / (1000 * 60); // minutes
            return sum + elapsed;
        }, 0);
        
        return Math.round(totalTime / allOrders.length);
    }

    updateOrderLists() {
        this.renderOrderList('pending', this.orders.pending);
        this.renderOrderList('cooking', this.orders.cooking);
        this.renderOrderList('ready', this.orders.ready);
    }

    renderOrderList(status, orders) {
        const container = document.getElementById(`${status}OrdersList`);
        
        if (orders.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-neutral-400">
                    <i data-lucide="check-circle" class="w-8 h-8 mx-auto mb-2 opacity-50"></i>
                    <p class="text-sm">Aucune commande ${this.getStatusText(status)}</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = orders.map(order => this.renderOrderCard(order, status)).join('');
        
        // Reinitialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    renderOrderCard(order, status) {
        const elapsed = Math.floor((Date.now() - order.time.getTime()) / (1000 * 60));
        const urgentClass = elapsed > 15 ? 'border-red-500/50 bg-red-500/10' : '';
        const priorityClass = order.priority === 'high' ? 'border-yellow-500/50' : '';
        
        return `
            <div class="kitchen-order-card ${urgentClass} ${priorityClass}" onclick="openOrderModal(${order.id})">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
                            <span class="text-white font-bold text-sm">${order.table}</span>
                        </div>
                        <div>
                            <div class="font-medium text-neutral-100">Table ${order.table}</div>
                            <div class="text-xs text-neutral-400">${elapsed}min</div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        ${order.priority === 'high' ? '<i data-lucide="alert-triangle" class="w-4 h-4 text-yellow-400"></i>' : ''}
                        ${elapsed > 15 ? '<i data-lucide="clock" class="w-4 h-4 text-red-400 animate-pulse"></i>' : ''}
                    </div>
                </div>
                
                <div class="space-y-2 mb-4">
                    ${order.items.map(item => `
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-neutral-200">${item.quantity}x ${item.name}</span>
                            ${item.notes ? `<span class="text-xs text-yellow-400">${item.notes}</span>` : ''}
                        </div>
                    `).join('')}
                </div>
                
                <div class="flex gap-2">
                    ${this.getOrderActions(order, status)}
                </div>
            </div>
        `;
    }

    getOrderActions(order, status) {
        switch(status) {
            case 'pending':
                return `
                    <button onclick="event.stopPropagation(); startCooking(${order.id})" class="btn-primary-modern flex-1 text-xs py-2">
                        <i data-lucide="play" class="w-3 h-3"></i>
                        Commencer
                    </button>
                `;
            case 'cooking':
                return `
                    <button onclick="event.stopPropagation(); markReady(${order.id})" class="btn-secondary-modern flex-1 text-xs py-2">
                        <i data-lucide="check" class="w-3 h-3"></i>
                        Prêt
                    </button>
                `;
            case 'ready':
                return `
                    <button onclick="event.stopPropagation(); markServed(${order.id})" class="btn-accent-modern flex-1 text-xs py-2">
                        <i data-lucide="truck" class="w-3 h-3"></i>
                        Servi
                    </button>
                `;
            default:
                return '';
        }
    }

    getStatusText(status) {
        const statusMap = {
            pending: 'en attente',
            cooking: 'en préparation',
            ready: 'prêtes à servir'
        };
        return statusMap[status] || status;
    }

    async startCooking(orderId) {
        const order = this.findOrder(orderId, 'pending');
        if (order) {
            this.moveOrder(order, 'pending', 'cooking');
            showNotification(`Préparation commencée pour la table ${order.table}`, 'info');
        }
    }

    async markReady(orderId) {
        const order = this.findOrder(orderId, 'cooking');
        if (order) {
            this.moveOrder(order, 'cooking', 'ready');
            showNotification(`Commande prête pour la table ${order.table}`, 'success');
        }
    }

    async markServed(orderId) {
        const order = this.findOrder(orderId, 'ready');
        if (order) {
            this.removeOrder(order, 'ready');
            showNotification(`Commande servie pour la table ${order.table}`, 'success');
        }
    }

    findOrder(orderId, status) {
        return this.orders[status].find(order => order.id === orderId);
    }

    moveOrder(order, fromStatus, toStatus) {
        const fromIndex = this.orders[fromStatus].indexOf(order);
        if (fromIndex > -1) {
            this.orders[fromStatus].splice(fromIndex, 1);
            order.status = toStatus;
            this.orders[toStatus].push(order);
            this.updateDisplay();
        }
    }

    removeOrder(order, status) {
        const index = this.orders[status].indexOf(order);
        if (index > -1) {
            this.orders[status].splice(index, 1);
            this.updateDisplay();
        }
    }

    openOrderModal(orderId) {
        // Find order in all statuses
        let order = null;
        for (const status in this.orders) {
            order = this.findOrder(orderId, status);
            if (order) break;
        }
        
        if (!order) return;
        
        this.selectedOrder = order;
        const modal = document.getElementById('orderModal');
        const title = document.getElementById('modalOrderTitle');
        const content = document.getElementById('orderModalContent');
        
        title.textContent = `Commande Table ${order.table}`;
        content.innerHTML = this.generateOrderModalContent(order);
        
        modal.classList.add('open');
        
        // Reinitialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    generateOrderModalContent(order) {
        const elapsed = Math.floor((Date.now() - order.time.getTime()) / (1000 * 60));
        
        return `
            <div class="space-y-6">
                <div class="grid grid-cols-2 gap-4">
                    <div class="glass-card p-4">
                        <h5 class="text-sm font-medium text-neutral-300 mb-2">Informations</h5>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-neutral-400">Table:</span>
                                <span class="text-neutral-100">${order.table}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-neutral-400">Temps écoulé:</span>
                                <span class="text-neutral-100">${elapsed} minutes</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-neutral-400">Statut:</span>
                                <span class="text-${this.getStatusColor(order.status)}-400">${this.getStatusText(order.status)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-neutral-400">Priorité:</span>
                                <span class="text-${order.priority === 'high' ? 'yellow' : 'neutral'}-400">${order.priority === 'high' ? 'Haute' : 'Normale'}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="glass-card p-4">
                        <h5 class="text-sm font-medium text-neutral-300 mb-2">Articles</h5>
                        <div class="space-y-2">
                            ${order.items.map(item => `
                                <div class="flex justify-between items-start text-sm">
                                    <div>
                                        <span class="text-neutral-100">${item.quantity}x ${item.name}</span>
                                        ${item.notes ? `<div class="text-xs text-yellow-400">${item.notes}</div>` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                
                <div class="flex gap-3">
                    <button onclick="closeOrderModal()" class="btn-ghost-modern flex-1">
                        <i data-lucide="x" class="w-4 h-4"></i>
                        Fermer
                    </button>
                    ${this.getModalActions(order)}
                </div>
            </div>
        `;
    }

    getModalActions(order) {
        switch(order.status) {
            case 'pending':
                return `<button onclick="startCooking(${order.id}); closeOrderModal()" class="btn-primary-modern flex-1">
                    <i data-lucide="play" class="w-4 h-4"></i>
                    Commencer la préparation
                </button>`;
            case 'cooking':
                return `<button onclick="markReady(${order.id}); closeOrderModal()" class="btn-secondary-modern flex-1">
                    <i data-lucide="check" class="w-4 h-4"></i>
                    Marquer comme prêt
                </button>`;
            case 'ready':
                return `<button onclick="markServed(${order.id}); closeOrderModal()" class="btn-accent-modern flex-1">
                    <i data-lucide="truck" class="w-4 h-4"></i>
                    Marquer comme servi
                </button>`;
            default:
                return '';
        }
    }

    getStatusColor(status) {
        const colors = {
            pending: 'red',
            cooking: 'yellow',
            ready: 'accent'
        };
        return colors[status] || 'neutral';
    }

    closeOrderModal() {
        const modal = document.getElementById('orderModal');
        modal.classList.remove('open');
        this.selectedOrder = null;
    }

    refreshOrders() {
        showNotification('Actualisation des commandes...', 'info', 1000);
        this.loadOrders();
    }

    startAutoRefresh() {
        // Refresh every 30 seconds
        this.refreshInterval = setInterval(() => {
            this.loadOrders();
        }, 30000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.kitchenManager = new KitchenManager();
});

// Global functions for template compatibility
window.openOrderModal = (orderId) => window.kitchenManager?.openOrderModal(orderId);
window.closeOrderModal = () => window.kitchenManager?.closeOrderModal();
window.startCooking = (orderId) => window.kitchenManager?.startCooking(orderId);
window.markReady = (orderId) => window.kitchenManager?.markReady(orderId);
window.markServed = (orderId) => window.kitchenManager?.markServed(orderId);
window.refreshOrders = () => window.kitchenManager?.refreshOrders();
