{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- En-tête -->
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Commandes en cours</h2>
        <div class="flex space-x-3">
            <a href="{{ url_for('pos.new_sale') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i> Nouvelle vente
            </a>
        </div>
    </div>

    <!-- Liste des commandes en cours -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Table</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Articles</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Statut</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {% for sale in pending_sales %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">#{{ sale.id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ sale.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">Table {{ sale.table_number }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ sale.items|length }} articles</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ sale.total_amount|round(2) }} €</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                En cours
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button type="button" class="text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 mr-3" data-sale-id="{{ sale.id }}" onclick="viewSaleDetails(this.dataset.saleId)">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="text-green-400 hover:text-green-500 dark:hover:text-green-300 mr-3" data-sale-id="{{ sale.id }}" onclick="completeSale(this.dataset.saleId)">
                                <i class="fas fa-check"></i>
                            </button>
                            <button type="button" class="text-red-400 hover:text-red-500 dark:hover:text-red-300" data-sale-id="{{ sale.id }}" onclick="cancelSale(this.dataset.saleId)">
                                <i class="fas fa-times"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal des détails de vente -->
<div id="saleDetailsModal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
            <div>
                <div class="mt-3 text-center sm:mt-5">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                        Détails de la commande
                    </h3>
                    <div class="mt-4">
                        <div id="saleDetails" class="space-y-4">
                            <!-- Les détails seront chargés dynamiquement -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-5 sm:mt-6">
                <button type="button" class="w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm" onclick="closeSaleDetailsModal()">
                    Fermer
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Fonction pour afficher les détails d'une vente
    function viewSaleDetails(id) {
        fetch(`/pos/sale_details/${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const details = document.getElementById('saleDetails');
                    details.innerHTML = `
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Articles</h4>
                                <ul class="mt-2 space-y-2">
                                    ${data.sale.items.map(item => `
                                        <li class="flex justify-between text-sm">
                                            <span class="text-gray-900 dark:text-white">${item.name} x${item.quantity}</span>
                                            <span class="text-gray-900 dark:text-white">${item.total} €</span>
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                            <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500 dark:text-gray-400">Sous-total</span>
                                    <span class="text-gray-900 dark:text-white">${data.sale.subtotal} €</span>
                                </div>
                                <div class="flex justify-between text-sm mt-2">
                                    <span class="text-gray-500 dark:text-gray-400">TVA</span>
                                    <span class="text-gray-900 dark:text-white">${data.sale.tax} €</span>
                                </div>
                                <div class="flex justify-between text-base font-medium mt-4">
                                    <span class="text-gray-900 dark:text-white">Total</span>
                                    <span class="text-gray-900 dark:text-white">${data.sale.total} €</span>
                                </div>
                            </div>
                        </div>
                    `;
                    document.getElementById('saleDetailsModal').classList.remove('hidden');
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue');
            });
    }

    // Fonction pour fermer le modal des détails
    function closeSaleDetailsModal() {
        document.getElementById('saleDetailsModal').classList.add('hidden');
    }

    // Fonction pour compléter une vente
    function completeSale(id) {
        if (confirm('Êtes-vous sûr de vouloir marquer cette commande comme terminée ?')) {
            fetch(`/pos/complete_sale/${id}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue');
            });
        }
    }

    // Fonction pour annuler une vente
    function cancelSale(id) {
        if (confirm('Êtes-vous sûr de vouloir annuler cette commande ?')) {
            fetch(`/pos/cancel_sale/${id}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue');
            });
        }
    }
</script>
{% endblock %} 