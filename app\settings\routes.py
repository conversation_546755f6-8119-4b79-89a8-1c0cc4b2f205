from flask import render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from . import settings_bp
from app.models.models import Business, User, Category
from app import db
from werkzeug.security import generate_password_hash

@settings_bp.route('/profile')
@login_required
def profile():
    return render_template('settings/profile.html', title='Mon profil')

@settings_bp.route('/profile', methods=['POST'])
@login_required
def update_profile():
    user = current_user
    
    # Mise à jour des informations de base
    user.first_name = request.form.get('first_name')
    user.last_name = request.form.get('last_name')
    user.email = request.form.get('email')
    user.phone = request.form.get('phone')
    
    # Mise à jour du mot de passe si fourni
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')
    
    if current_password and new_password and confirm_password:
        if not user.check_password(current_password):
            flash('Le mot de passe actuel est incorrect', 'danger')
            return redirect(url_for('settings.profile'))
        
        if new_password != confirm_password:
            flash('Les nouveaux mots de passe ne correspondent pas', 'danger')
            return redirect(url_for('settings.profile'))
        
        user.password_hash = generate_password_hash(new_password)
    
    db.session.commit()
    flash('Profil mis à jour avec succès', 'success')
    return redirect(url_for('settings.profile'))

@settings_bp.route('/business')
@login_required
def business():
    business = current_user.owned_business[0]
    return render_template('settings/business.html', title='Paramètres de l\'entreprise', business=business)

@settings_bp.route('/business', methods=['POST'])
@login_required
def update_business():
    business = current_user.owned_business[0]
    
    # Mise à jour des informations de l'entreprise
    business.name = request.form.get('name')
    business.address = request.form.get('address')
    business.phone = request.form.get('phone')
    business.email = request.form.get('email')
    business.tax_number = request.form.get('tax_number')
    
    # Mise à jour du logo si fourni
    if 'logo' in request.files:
        logo = request.files['logo']
        if logo.filename:
            # Sauvegarder le logo
            filename = secure_filename(logo.filename)
            logo_path = os.path.join('app/static/uploads/logos', filename)
            logo.save(logo_path)
            business.logo_path = f'/static/uploads/logos/{filename}'
    
    db.session.commit()
    flash('Paramètres de l\'entreprise mis à jour avec succès', 'success')
    return redirect(url_for('settings.business'))

@settings_bp.route('/users')
@login_required
def users():
    if current_user.role != 'owner':
        flash('Accès non autorisé', 'error')
        return redirect(url_for('main.index'))
    
    users = User.query.filter_by(owner_id=current_user.id).all()
    return render_template('settings/users.html',
                         title='Gestion des utilisateurs',
                         users=users)

@settings_bp.route('/users/add', methods=['GET', 'POST'])
@login_required
def add_user():
    if current_user.role != 'owner':
        flash('Accès non autorisé', 'error')
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        user = User(
            username=request.form.get('username'),
            email=request.form.get('email'),
            role=request.form.get('role', 'caissier'),
            owner_id=current_user.id
        )
        user.set_password(request.form.get('password'))
        
        db.session.add(user)
        db.session.commit()
        
        flash('Utilisateur ajouté avec succès', 'success')
        return redirect(url_for('settings.users'))
    
    return render_template('settings/add_user.html', title='Ajouter un utilisateur')

@settings_bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    if current_user.role != 'owner':
        flash('Accès non autorisé', 'error')
        return redirect(url_for('main.index'))
    
    user = User.query.get_or_404(user_id)
    if user.owner_id != current_user.id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        user.username = request.form.get('username')
        user.email = request.form.get('email')
        user.role = request.form.get('role')
        
        if request.form.get('password'):
            user.set_password(request.form.get('password'))
        
        db.session.commit()
        flash('Utilisateur mis à jour avec succès', 'success')
        return redirect(url_for('settings.users'))
    
    return render_template('settings/edit_user.html', title='Modifier un utilisateur', user=user)

@settings_bp.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
def delete_user(user_id):
    if current_user.role != 'owner':
        return jsonify({'success': False, 'message': 'Accès non autorisé'})
    
    user = User.query.get_or_404(user_id)
    if user.owner_id != current_user.id:
        return jsonify({'success': False, 'message': 'Accès non autorisé'})
    
    if user.id == current_user.id:
        return jsonify({'success': False, 'message': 'Vous ne pouvez pas supprimer votre propre compte'})
    
    db.session.delete(user)
    db.session.commit()
    
    return jsonify({'success': True, 'message': 'Utilisateur supprimé avec succès'})

@settings_bp.route('/categories')
@login_required
def categories():
    categories = Category.query.filter_by(business_id=current_user.owned_business[0].id).all()
    return render_template('settings/categories.html', title='Gestion des catégories', categories=categories)

@settings_bp.route('/categories/add', methods=['GET', 'POST'])
@login_required
def add_category():
    if request.method == 'POST':
        category = Category(
            name=request.form.get('name'),
            type=request.form.get('type'),
            business_id=current_user.owned_business[0].id
        )
        
        db.session.add(category)
        db.session.commit()
        
        flash('Catégorie ajoutée avec succès', 'success')
        return redirect(url_for('settings.categories'))
    
    return render_template('settings/add_category.html', title='Ajouter une catégorie')

@settings_bp.route('/categories/<int:category_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_category(category_id):
    category = Category.query.get_or_404(category_id)
    if category.business_id != current_user.owned_business[0].id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        category.name = request.form.get('name')
        category.type = request.form.get('type')
        
        db.session.commit()
        flash('Catégorie mise à jour avec succès', 'success')
        return redirect(url_for('settings.categories'))
    
    return render_template('settings/edit_category.html', title='Modifier une catégorie', category=category)

@settings_bp.route('/categories/<int:category_id>/delete', methods=['POST'])
@login_required
def delete_category(category_id):
    category = Category.query.get_or_404(category_id)
    if category.business_id != current_user.owned_business[0].id:
        return jsonify({'success': False, 'message': 'Accès non autorisé'})
    
    if category.products:
        return jsonify({'success': False, 'message': 'Impossible de supprimer une catégorie contenant des produits'})
    
    db.session.delete(category)
    db.session.commit()
    
    return jsonify({'success': True, 'message': 'Catégorie supprimée avec succès'}) 