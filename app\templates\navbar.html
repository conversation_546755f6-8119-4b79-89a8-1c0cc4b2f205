<!-- Modern Futuristic Navigation -->
<nav class="fixed top-0 left-0 right-0 z-40 glass-card border-b border-white/10">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
            <!-- Logo & Brand -->
            <div class="flex items-center animate-fade-in-up">
                <a href="{{ url_for('main.index') }}" class="flex items-center space-x-3 group">
                    <div class="relative">
                        <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center shadow-lg group-hover:shadow-neon transition-all duration-300 group-hover:scale-110">
                            <i data-lucide="store" class="w-5 h-5 text-white"></i>
                        </div>
                        <div class="absolute -top-1 -right-1 w-3 h-3 bg-accent-400 rounded-full animate-pulse"></div>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                            FuturePOS
                        </span>
                        <span class="text-xs text-neutral-400 font-medium">Restaurant System</span>
                    </div>
                </a>
            </div>

            {% if current_user.is_authenticated %}
            <!-- Main Navigation -->
            <div class="hidden lg:flex items-center space-x-1 animate-fade-in-up stagger-children">
                <!-- POS/Ventes -->
                <div class="relative group">
                    <button class="nav-item-modern group">
                        <i data-lucide="shopping-cart" class="w-4 h-4"></i>
                        <span>POS</span>
                        <i data-lucide="chevron-down" class="w-3 h-3 transition-transform group-hover:rotate-180"></i>
                    </button>
                    <div class="nav-dropdown-modern">
                        <div class="p-2 space-y-1">
                            <a href="{{ url_for('pos.new_sale') }}" class="nav-dropdown-item-modern">
                                <i data-lucide="plus-circle" class="w-4 h-4"></i>
                                <div>
                                    <div class="font-medium">Nouvelle Vente</div>
                                    <div class="text-xs text-neutral-400">Créer une commande</div>
                                </div>
                            </a>
                            <a href="{{ url_for('pos.pending_sales') }}" class="nav-dropdown-item-modern">
                                <i data-lucide="clock" class="w-4 h-4"></i>
                                <div>
                                    <div class="font-medium">Commandes en cours</div>
                                    <div class="text-xs text-neutral-400">Gérer les commandes</div>
                                </div>
                            </a>
                            <a href="{{ url_for('pos.sales_history') }}" class="nav-dropdown-item-modern">
                                <i data-lucide="history" class="w-4 h-4"></i>
                                <div>
                                    <div class="font-medium">Historique</div>
                                    <div class="text-xs text-neutral-400">Voir les ventes</div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                    <!-- Inventaire -->
                    <div class="relative group">
                        <button class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium flex items-center transition-colors duration-200">
                            <i class="fas fa-boxes mr-2"></i>
                            Inventaire
                            <i class="fas fa-chevron-down ml-2 text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-48 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform origin-top scale-95 group-hover:scale-100">
                            <div class="py-1">
                                <a href="{{ url_for('inventory.products') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                    <i class="fas fa-box mr-2"></i> Produits
                                </a>
                                <a href="{{ url_for('inventory.categories') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                    <i class="fas fa-tags mr-2"></i> Catégories
                                </a>
                                <a href="{{ url_for('inventory.low_stock') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                    <span class="flex items-center">
                                        <i class="fas fa-exclamation-triangle mr-2"></i>
                                        Stock bas
                                        {% if low_stock_count > 0 %}
                                        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                            {{ low_stock_count }}
                                        </span>
                                        {% endif %}
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Rapports -->
                    <div class="relative group">
                        <button class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium flex items-center transition-colors duration-200">
                            <i class="fas fa-chart-bar mr-2"></i>
                            Rapports
                            <i class="fas fa-chevron-down ml-2 text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-48 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform origin-top scale-95 group-hover:scale-100">
                            <div class="py-1">
                                <a href="{{ url_for('reports.sales_report') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                    <i class="fas fa-chart-line mr-2"></i> Ventes
                                </a>
                                <a href="{{ url_for('reports.products_report') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                    <i class="fas fa-box mr-2"></i> Produits
                                </a>
                                <a href="{{ url_for('reports.stock_report') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                    <i class="fas fa-warehouse mr-2"></i> Stocks
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Caisse -->
                    <div class="relative group">
                        <button class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium flex items-center transition-colors duration-200">
                            <i class="fas fa-cash-register mr-2"></i>
                            Caisse
                            <i class="fas fa-chevron-down ml-2 text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-48 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform origin-top scale-95 group-hover:scale-100">
                            <div class="py-1">
                                <a href="{{ url_for('pos.cash_register') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                    <i class="fas fa-money-bill-wave mr-2"></i> Fond de caisse
                                </a>
                                <a href="{{ url_for('pos.cash_movements') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                    <i class="fas fa-exchange-alt mr-2"></i> Mouvements
                                </a>
                                <a href="{{ url_for('pos.z_report') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                    <i class="fas fa-file-invoice mr-2"></i> Ticket Z
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Paramètres -->
                    <div class="relative group">
                        <button class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium flex items-center transition-colors duration-200">
                            <i class="fas fa-cog mr-2"></i>
                            Paramètres
                            <i class="fas fa-chevron-down ml-2 text-xs"></i>
                        </button>
                        <div class="absolute left-0 mt-2 w-48 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform origin-top scale-95 group-hover:scale-100">
                            <div class="py-1">
                                <a href="{{ url_for('settings.users') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                    <i class="fas fa-users mr-2"></i> Utilisateurs
                                </a>
                                <a href="{{ url_for('settings.business') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                    <i class="fas fa-building mr-2"></i> Entreprise
                                </a>
                                <a href="{{ url_for('settings.categories') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                    <i class="fas fa-tags mr-2"></i> Catégories
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Menu -->
            <div class="relative group">
                <button class="flex items-center text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                    <span class="mr-2">{{ current_user.username }}</span>
                    <i class="fas fa-chevron-down text-xs"></i>
                </button>
                <div class="absolute right-0 mt-2 w-48 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform origin-top scale-95 group-hover:scale-100">
                    <div class="py-1">
                        <a href="{{ url_for('main.profile') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                            <i class="fas fa-user mr-2"></i> Profil
                        </a>
                        <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                        <a href="{{ url_for('auth.logout') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                            <i class="fas fa-sign-out-alt mr-2"></i> Déconnexion
                        </a>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="flex items-center space-x-4">
                <a href="{{ url_for('auth.login') }}" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                    <i class="fas fa-sign-in-alt mr-2"></i> Connexion
                </a>
                <a href="{{ url_for('auth.register') }}" class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105">
                    <i class="fas fa-user-plus mr-2"></i> Inscription
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</nav>