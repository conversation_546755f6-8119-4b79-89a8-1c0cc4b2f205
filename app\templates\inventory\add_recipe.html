{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title mb-0">Ajouter une recette</h2>
                </div>
                <div class="card-body">
                    <form method="POST" id="recipeForm">
                        <div class="mb-3">
                            <label for="name" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Ingrédients</label>
                            <div id="ingredientsList">
                                <div class="ingredient-item mb-3">
                                    <div class="row">
                                        <div class="col-md-5">
                                            <select class="form-select ingredient-select" name="ingredients[]" required>
                                                <option value="">Sélectionner un ingrédient</option>
                                                {% for ingredient in ingredients %}
                                                <option value="{{ ingredient.id }}">{{ ingredient.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <input type="number" class="form-control" name="quantities[]" 
                                                   placeholder="Quantité" step="0.01" min="0" required>
                                        </div>
                                        <div class="col-md-3">
                                            <select class="form-select" name="units[]" required>
                                                <option value="kg">Kilogramme (kg)</option>
                                                <option value="g">Gramme (g)</option>
                                                <option value="l">Litre (l)</option>
                                                <option value="ml">Millilitre (ml)</option>
                                                <option value="unit">Unité</option>
                                            </select>
                                        </div>
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-danger remove-ingredient" 
                                                    onclick="removeIngredient(this)">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-secondary mt-2" onclick="addIngredient()">
                                <i class="fas fa-plus"></i> Ajouter un ingrédient
                            </button>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('inventory.recipes') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addIngredient() {
    const ingredientsList = document.getElementById('ingredientsList');
    const newIngredient = document.querySelector('.ingredient-item').cloneNode(true);
    
    // Réinitialiser les valeurs
    newIngredient.querySelectorAll('input, select').forEach(input => {
        input.value = '';
    });
    
    ingredientsList.appendChild(newIngredient);
}

function removeIngredient(button) {
    const ingredientsList = document.getElementById('ingredientsList');
    if (ingredientsList.children.length > 1) {
        button.closest('.ingredient-item').remove();
    }
}

// Empêcher la soumission du formulaire si aucun ingrédient n'est sélectionné
document.getElementById('recipeForm').addEventListener('submit', function(e) {
    const ingredients = document.querySelectorAll('.ingredient-select');
    let hasSelectedIngredient = false;
    
    ingredients.forEach(select => {
        if (select.value) {
            hasSelectedIngredient = true;
        }
    });
    
    if (!hasSelectedIngredient) {
        e.preventDefault();
        alert('Veuillez ajouter au moins un ingrédient à la recette');
    }
});
</script>
{% endblock %} 