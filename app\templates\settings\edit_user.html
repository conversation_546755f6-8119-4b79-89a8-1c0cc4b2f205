{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">Modifier un utilisateur</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('settings.edit_user', user_id=user.id) }}">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Prénom</label>
                                <input type="text" class="form-control" name="first_name" value="{{ user.first_name }}" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Nom</label>
                                <input type="text" class="form-control" name="last_name" value="{{ user.last_name }}" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" value="{{ user.email }}" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Téléphone</label>
                                <input type="tel" class="form-control" name="phone" value="{{ user.phone }}">
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Nouveau mot de passe</label>
                                <input type="password" class="form-control" name="password">
                                <small class="text-muted">Laissez vide pour ne pas modifier le mot de passe</small>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="is_admin" id="is_admin" {% if user.is_admin %}checked{% endif %}>
                                    <label class="form-check-label" for="is_admin">Administrateur</label>
                                </div>
                                <small class="text-muted">Les administrateurs ont accès à toutes les fonctionnalités du système</small>
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <a href="{{ url_for('settings.users') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 