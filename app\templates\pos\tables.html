{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- En-tête -->
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Gestion des tables</h2>
        <div class="flex space-x-3">
            <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200" onclick="openAddTableModal()">
                <i class="fas fa-plus mr-2"></i> Ajouter une table
            </button>
        </div>
    </div>

    <!-- Grille des tables -->
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {% for table in tables %}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
            <div class="p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Table {{ table.number }}</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ table.capacity }} places</p>
                    </div>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if table.status == 'free' %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200{% elif table.status == 'occupied' %}bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200{% else %}bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200{% endif %},">
                        {% if table.status == 'free' %}
                            Libre
                        {% elif table.status == 'occupied' %}
                            Occupée
                        {% else %}
                            Réservée
                        {% endif %}
                    </span>
                </div>
                <div class="mt-4 flex justify-end space-x-2">
                    <button type="button" class="text-blue-400 hover:text-blue-500 dark:hover:text-blue-300" data-table-id="{{ table.id }}" onclick="editTable(this.dataset.tableId)">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="text-red-400 hover:text-red-500 dark:hover:text-red-300" data-table-id="{{ table.id }}" onclick="deleteTable(this.dataset.tableId)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Modal d'ajout de table -->
<div id="addTableModal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
            <div>
                <div class="mt-3 text-center sm:mt-5">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                        Nouvelle table
                    </h3>
                    <div class="mt-4">
                        <form id="addTableForm" class="space-y-4">
                            <div>
                                <label for="tableNumber" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Numéro de table</label>
                                <input type="number" name="tableNumber" id="tableNumber" min="1" class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white" required>
                            </div>
                            <div>
                                <label for="capacity" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Capacité</label>
                                <input type="number" name="capacity" id="capacity" min="1" class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white" required>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm" onclick="submitAddTable()">
                    Ajouter
                </button>
                <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm" onclick="closeAddTableModal()">
                    Annuler
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Fonction pour ouvrir le modal d'ajout de table
    function openAddTableModal() {
        document.getElementById('addTableModal').classList.remove('hidden');
    }

    // Fonction pour fermer le modal d'ajout de table
    function closeAddTableModal() {
        document.getElementById('addTableModal').classList.add('hidden');
    }

    // Fonction pour soumettre l'ajout d'une table
    function submitAddTable() {
        const form = document.getElementById('addTableForm');
        const formData = new FormData(form);
        
        fetch('/pos/add_table', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue');
        });
    }

    // Fonction pour éditer une table
    function editTable(id) {
        // Implémenter la logique d'édition
    }

    // Fonction pour supprimer une table
    function deleteTable(id) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette table ?')) {
            fetch(`/pos/delete_table/${id}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue');
            });
        }
    }
</script>
{% endblock %} 