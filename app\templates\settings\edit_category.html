{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">Modifier une catégorie</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('settings.edit_category', category_id=category.id) }}">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Nom</label>
                                <input type="text" class="form-control" name="name" value="{{ category.name }}" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" name="description" rows="3">{{ category.description }}</textarea>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Type</label>
                                <select class="form-select" name="type" required>
                                    <option value="product" {% if category.type == 'product' %}selected{% endif %}>Produit</option>
                                    <option value="service" {% if category.type == 'service' %}selected{% endif %}>Service</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Nombre de produits</label>
                                <input type="text" class="form-control" value="{{ category.products|length }}" disabled>
                                <small class="text-muted">Une catégorie contenant des produits ne peut pas être supprimée</small>
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <a href="{{ url_for('settings.categories') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 