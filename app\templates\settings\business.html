{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">Paramètres de l'entreprise</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('settings.business') }}" enctype="multipart/form-data">
                        <div class="row mb-4">
                            <div class="col-md-12 text-center">
                                <div class="mb-3">
                                    {% if business.logo_path %}
                                        <img src="{{ business.logo_path }}" alt="Logo" class="img-thumbnail" style="max-width: 200px;">
                                    {% else %}
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 200px; height: 200px; margin: 0 auto;">
                                            <i class="fas fa-building fa-3x text-muted"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Logo de l'entreprise</label>
                                    <input type="file" class="form-control" name="logo" accept="image/*">
                                    <small class="text-muted">Format recommandé : PNG ou JPG, max 2MB</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Nom de l'entreprise</label>
                                <input type="text" class="form-control" name="name" value="{{ business.name }}" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Adresse</label>
                                <textarea class="form-control" name="address" rows="3">{{ business.address }}</textarea>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Téléphone</label>
                                <input type="tel" class="form-control" name="phone" value="{{ business.phone }}">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" value="{{ business.email }}">
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Numéro de TVA</label>
                                <input type="text" class="form-control" name="tax_number" value="{{ business.tax_number }}">
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 