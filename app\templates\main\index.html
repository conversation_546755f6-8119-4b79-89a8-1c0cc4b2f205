{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
{% if current_user.is_authenticated %}
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Statistiques du jour -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Ventes du jour</h3>
                <div class="mt-2">
                    <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ daily_total|round(2) }} €</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ daily_count }} ventes</p>
                </div>
            </div>
        </div>

        <!-- Produits en rupture de stock -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Produits en rupture de stock</h3>
                <div class="mt-4">
                    {% if low_stock_products %}
                        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                            {% for product in low_stock_products %}
                                <li class="py-3 flex justify-between items-center">
                                    <span class="text-sm text-gray-900 dark:text-white">{{ product.name }}</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                        {{ product.stock }} {{ product.unit }}
                                    </span>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-sm text-gray-500 dark:text-gray-400">Aucun produit en rupture de stock</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Catégories populaires -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Catégories populaires</h3>
                <div class="mt-4">
                    {% if popular_categories %}
                        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                            {% for category, sale_count in popular_categories %}
                                <li class="py-3 flex justify-between items-center">
                                    <span class="text-sm text-gray-900 dark:text-white">{{ category.name }}</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                        {{ sale_count }} ventes
                                    </span>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-sm text-gray-500 dark:text-gray-400">Aucune vente aujourd'hui</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Dernières ventes -->
    <div class="mt-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Dernières ventes</h3>
                {% if daily_sales %}
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Montant</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Méthode de paiement</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Statut</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                {% for sale in daily_sales %}
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ sale.id }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ sale.created_at.strftime('%H:%M') }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ sale.total_amount|round(2) }} €</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ sale.payment_method }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' if sale.status == 'paid' else 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' }}">
                                                {{ sale.status }}
                                            </span>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-sm text-gray-500 dark:text-gray-400">Aucune vente aujourd'hui</p>
                {% endif %}
            </div>
        </div>
    </div>
{% else %}
    <div class="min-h-[80vh] flex items-center justify-center">
        <div class="max-w-3xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-extrabold text-gray-900 dark:text-white sm:text-5xl sm:tracking-tight lg:text-6xl">
                Bienvenue sur POS System
            </h1>
            <p class="mt-6 text-xl text-gray-500 dark:text-gray-400">
                Une solution complète pour gérer votre entreprise
            </p>
            <div class="mt-10 flex justify-center gap-4">
                <a href="{{ url_for('auth.register') }}" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                    Commencer
                </a>
                <a href="{{ url_for('auth.login') }}" class="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                    Se connecter
                </a>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %} 