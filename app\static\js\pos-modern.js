/* ===== MODERN POS INTERFACE JAVASCRIPT ===== */

class ModernPOS {
    constructor() {
        this.cart = [];
        this.selectedProduct = null;
        this.currentQuantity = 1;
        this.currentView = 'grid';
        this.currentSort = 'name';
        this.searchTerm = '';
        this.selectedCategory = 'all';

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupKeyboardShortcuts();
        this.loadCart();
        this.updateCartDisplay();

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        console.log('Modern POS initialized');
    }

    setupEventListeners() {
        // Product cards
        document.querySelectorAll('.product-card-modern').forEach(card => {
            card.addEventListener('click', (e) => {
                if (!e.target.closest('button')) {
                    this.selectProduct(card);
                }
            });
        });

        // Category buttons
        document.querySelectorAll('.category-btn-modern').forEach(btn => {
            btn.addEventListener('click', () => this.filterByCategory(btn.dataset.category));
        });

        // Search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
        }

        // View toggle buttons
        document.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.addEventListener('click', () => this.toggleView(btn.dataset.view));
        });

        // Quantity input
        const quantityInput = document.getElementById('quantityInput');
        if (quantityInput) {
            quantityInput.addEventListener('input', (e) => this.updateQuantity(e.target.value));
            quantityInput.addEventListener('focus', () => this.highlightQuantityInput());
            quantityInput.addEventListener('blur', () => this.unhighlightQuantityInput());
        }

        // Sort select
        const sortSelect = document.querySelector('select');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => this.sortProducts(e.target.value));
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+F for search
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                document.getElementById('searchInput')?.focus();
            }

            // Escape to clear selection
            if (e.key === 'Escape') {
                this.clearSelection();
            }

            // Enter to add selected product
            if (e.key === 'Enter' && this.selectedProduct) {
                e.preventDefault();
                this.addToCart();
            }

            // Number keys for quantity
            if (e.key >= '0' && e.key <= '9' && !e.target.matches('input')) {
                e.preventDefault();
                this.appendToQuantity(e.key);
            }

            // Decimal point
            if (e.key === '.' && !e.target.matches('input')) {
                e.preventDefault();
                this.appendToQuantity('.');
            }

            // Backspace for quantity
            if (e.key === 'Backspace' && !e.target.matches('input')) {
                e.preventDefault();
                this.backspaceQuantity();
            }
        });
    }

    selectProduct(card) {
        // Clear previous selection
        document.querySelectorAll('.product-card-modern').forEach(c => {
            c.classList.remove('selected');
        });

        // Select new product
        card.classList.add('selected');
        this.selectedProduct = {
            id: card.dataset.productId,
            name: card.querySelector('.product-name-modern').textContent,
            price: parseFloat(card.dataset.price),
            element: card
        };

        // Add visual feedback
        this.showProductSelected();

        console.log('Product selected:', this.selectedProduct);
    }

    showProductSelected() {
        if (this.selectedProduct) {
            // Add glow effect
            this.selectedProduct.element.style.boxShadow = '0 0 30px rgba(91, 130, 255, 0.5)';

            // Show notification
            this.showNotification(`${this.selectedProduct.name} sélectionné`, 'info', 2000);
        }
    }

    clearSelection() {
        document.querySelectorAll('.product-card-modern').forEach(c => {
            c.classList.remove('selected');
            c.style.boxShadow = '';
        });
        this.selectedProduct = null;
    }

    updateQuantity(value) {
        this.currentQuantity = Math.max(0.1, Math.min(999, parseFloat(value) || 1));
        document.getElementById('quantityInput').value = this.currentQuantity;
    }

    setQuantity(value) {
        this.currentQuantity = value;
        document.getElementById('quantityInput').value = value;
        this.highlightQuantityInput();
    }

    clearQuantity() {
        this.setQuantity(1);
    }

    appendToQuantity(digit) {
        const input = document.getElementById('quantityInput');
        const currentValue = input.value;

        if (digit === '.' && currentValue.includes('.')) return;

        const newValue = currentValue === '1' ? digit : currentValue + digit;
        const numValue = parseFloat(newValue);

        if (numValue <= 999) {
            input.value = newValue;
            this.currentQuantity = numValue;
        }
    }

    backspaceQuantity() {
        const input = document.getElementById('quantityInput');
        const currentValue = input.value;
        const newValue = currentValue.length > 1 ? currentValue.slice(0, -1) : '1';

        input.value = newValue;
        this.currentQuantity = parseFloat(newValue);
    }

    highlightQuantityInput() {
        const border = document.getElementById('quantityBorder');
        if (border) {
            border.style.borderColor = 'var(--primary-500)';
        }
    }

    unhighlightQuantityInput() {
        const border = document.getElementById('quantityBorder');
        if (border) {
            border.style.borderColor = 'transparent';
        }
    }

    quickAdd(productId) {
        const card = document.querySelector(`[data-product-id="${productId}"]`);
        if (card) {
            this.selectProduct(card);
            this.addToCart();
        }
    }

    async addToCart() {
        if (!this.selectedProduct) {
            this.showNotification('Aucun produit sélectionné', 'warning');
            return;
        }

        const quantity = this.currentQuantity;
        if (quantity <= 0 || quantity > 999) {
            this.showNotification('Quantité invalide', 'error');
            return;
        }

        try {
            showLoading();

            const response = await fetch('/pos/add_to_cart', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    product_id: this.selectedProduct.id,
                    quantity: quantity
                })
            });

            const data = await response.json();

            if (data.success) {
                this.cart = data.cart;
                this.updateCartDisplay();
                this.showNotification(`${this.selectedProduct.name} ajouté au panier`, 'success');
                this.clearSelection();
                this.setQuantity(1);
            } else {
                this.showNotification(data.message || 'Erreur lors de l\'ajout', 'error');
            }
        } catch (error) {
            console.error('Error adding to cart:', error);
            this.showNotification('Erreur de connexion', 'error');
        } finally {
            hideLoading();
        }
    }

    filterByCategory(categoryId) {
        this.selectedCategory = categoryId;

        // Update active button
        document.querySelectorAll('.category-btn-modern').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.category === categoryId);
        });

        // Filter products
        this.applyFilters();
    }

    handleSearch(term) {
        this.searchTerm = term.toLowerCase();
        this.applyFilters();
    }

    applyFilters() {
        const products = document.querySelectorAll('.product-card-modern');

        products.forEach(product => {
            const matchesCategory = this.selectedCategory === 'all' ||
                                  product.dataset.category === this.selectedCategory;
            const matchesSearch = !this.searchTerm ||
                                product.dataset.name.includes(this.searchTerm);

            if (matchesCategory && matchesSearch) {
                product.style.display = '';
                product.classList.add('animate-fade-in-up');
            } else {
                product.style.display = 'none';
            }
        });
    }

    toggleView(view) {
        this.currentView = view;

        // Update active button
        document.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.view === view);
        });

        // Update grid layout
        const grid = document.getElementById('productsGrid');
        if (grid) {
            if (view === 'list') {
                grid.style.gridTemplateColumns = '1fr';
            } else {
                grid.style.gridTemplateColumns = 'repeat(auto-fill, minmax(180px, 1fr))';
            }
        }
    }

    sortProducts(sortBy) {
        this.currentSort = sortBy;
        const grid = document.getElementById('productsGrid');
        const products = Array.from(grid.children);

        products.sort((a, b) => {
            switch (sortBy) {
                case 'name':
                    return a.dataset.name.localeCompare(b.dataset.name);
                case 'price-asc':
                    return parseFloat(a.dataset.price) - parseFloat(b.dataset.price);
                case 'price-desc':
                    return parseFloat(b.dataset.price) - parseFloat(a.dataset.price);
                default:
                    return 0;
            }
        });

        // Re-append sorted products
        products.forEach(product => grid.appendChild(product));
    }

    async loadCart() {
        try {
            // Try to load cart from server session first
            const response = await fetch('/pos/get_cart', {
                method: 'GET',
                headers: {
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.cart) {
                    this.cart = data.cart;
                    this.updateCartDisplay();
                    return;
                }
            }
        } catch (error) {
            console.log('Could not load cart from server, using local storage');
        }

        // Fallback to session storage
        const savedCart = sessionStorage.getItem('pos_cart');
        if (savedCart) {
            try {
                this.cart = JSON.parse(savedCart);
                this.updateCartDisplay();
            } catch (error) {
                console.error('Error parsing saved cart:', error);
                this.cart = [];
            }
        }
    }

    updateCartDisplay() {
        const cartItems = document.getElementById('cartItems');
        const payButton = document.getElementById('payButton');

        if (!cartItems) return;

        // Clear current items
        cartItems.innerHTML = '';

        if (this.cart.length === 0) {
            // Show empty cart message
            cartItems.innerHTML = `
                <div class="text-center py-12 text-neutral-400">
                    <i data-lucide="shopping-cart" class="w-12 h-12 mx-auto mb-4 opacity-50"></i>
                    <p class="text-sm">Votre panier est vide</p>
                    <p class="text-xs mt-1">Sélectionnez des produits pour commencer</p>
                </div>
            `;
            payButton.disabled = true;
        } else {
            // Show cart items
            this.cart.forEach((item, index) => {
                const itemElement = document.createElement('div');
                itemElement.className = 'cart-item-modern animate-fade-in-up';
                itemElement.style.animationDelay = `${index * 0.1}s`;
                itemElement.innerHTML = `
                    <div class="cart-item-details-modern">
                        <div class="cart-item-name-modern">${item.name}</div>
                        <div class="cart-item-quantity-modern">
                            <div class="quantity-controls-modern">
                                <button class="quantity-btn-modern" onclick="window.modernPOS.updateItemQuantity(${item.product_id}, ${item.quantity - 0.5})">
                                    <i data-lucide="minus" class="w-3 h-3"></i>
                                </button>
                                <span class="px-2 font-mono">${item.quantity}</span>
                                <button class="quantity-btn-modern" onclick="window.modernPOS.updateItemQuantity(${item.product_id}, ${item.quantity + 0.5})">
                                    <i data-lucide="plus" class="w-3 h-3"></i>
                                </button>
                            </div>
                            <span class="text-neutral-500">× ${item.price.toFixed(2)} €</span>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="cart-item-price-modern">${(item.price * item.quantity).toFixed(2)} €</div>
                        <button class="quantity-btn-modern text-red-400 hover:text-red-300" onclick="window.modernPOS.removeFromCart(${item.product_id})">
                            <i data-lucide="trash-2" class="w-3 h-3"></i>
                        </button>
                    </div>
                `;
                cartItems.appendChild(itemElement);
            });
            payButton.disabled = false;
        }

        // Update totals
        this.updateTotals();

        // Reinitialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        // Save to session storage
        sessionStorage.setItem('pos_cart', JSON.stringify(this.cart));
    }

    updateTotals() {
        const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const tax = subtotal * 0.20; // 20% VAT
        const total = subtotal + tax;

        document.getElementById('subtotal').textContent = `${subtotal.toFixed(2)} €`;
        document.getElementById('tax').textContent = `${tax.toFixed(2)} €`;
        document.getElementById('total').textContent = `${total.toFixed(2)} €`;
    }

    async updateItemQuantity(productId, newQuantity) {
        if (newQuantity <= 0) {
            return this.removeFromCart(productId);
        }

        const item = this.cart.find(item => item.product_id == productId);
        if (item) {
            item.quantity = Math.min(999, Math.max(0.1, newQuantity));
            this.updateCartDisplay();
        }
    }

    async removeFromCart(productId) {
        try {
            const response = await fetch(`/pos/remove_from_cart/${productId}`, {
                method: 'POST',
                headers: {
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const data = await response.json();

            if (data.success) {
                this.cart = data.cart;
                this.updateCartDisplay();
                this.showNotification('Produit retiré du panier', 'success');
            } else {
                this.showNotification(data.message || 'Erreur lors de la suppression', 'error');
            }
        } catch (error) {
            console.error('Error removing from cart:', error);
            this.showNotification('Erreur de connexion', 'error');
        }
    }

    async clearCart() {
        try {
            const response = await fetch('/pos/clear_cart', {
                method: 'POST',
                headers: {
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const data = await response.json();

            if (data.success) {
                this.cart = [];
                this.updateCartDisplay();
                this.showNotification('Panier vidé', 'success');
            } else {
                this.showNotification(data.message || 'Erreur lors du vidage', 'error');
            }
        } catch (error) {
            console.error('Error clearing cart:', error);
            this.showNotification('Erreur de connexion', 'error');
        }
    }

    saveOrder() {
        if (this.cart.length === 0) {
            this.showNotification('Le panier est vide', 'warning');
            return;
        }

        // Save order to local storage for later
        const order = {
            id: Date.now(),
            items: [...this.cart],
            timestamp: new Date().toISOString(),
            status: 'saved'
        };

        const savedOrders = JSON.parse(localStorage.getItem('saved_orders') || '[]');
        savedOrders.push(order);
        localStorage.setItem('saved_orders', JSON.stringify(savedOrders));

        this.showNotification('Commande sauvegardée', 'success');
    }

    printReceipt() {
        if (this.cart.length === 0) {
            this.showNotification('Le panier est vide', 'warning');
            return;
        }

        // Create a simple receipt format
        const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const tax = subtotal * 0.20;
        const total = subtotal + tax;

        let receipt = `
=================================
         FUTUREPOS RESTAURANT
=================================
Date: ${new Date().toLocaleString('fr-FR')}

---------------------------------
ARTICLES:
---------------------------------
`;

        this.cart.forEach(item => {
            receipt += `${item.name}\n`;
            receipt += `  ${item.quantity} x ${item.price.toFixed(2)}€ = ${(item.price * item.quantity).toFixed(2)}€\n\n`;
        });

        receipt += `---------------------------------
Sous-total: ${subtotal.toFixed(2)}€
TVA (20%): ${tax.toFixed(2)}€
TOTAL: ${total.toFixed(2)}€
=================================
        Merci de votre visite !
=================================`;

        // Open print dialog with receipt
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>Reçu - FuturePOS</title>
                    <style>
                        body { font-family: 'Courier New', monospace; font-size: 12px; margin: 20px; }
                        pre { white-space: pre-wrap; }
                    </style>
                </head>
                <body>
                    <pre>${receipt}</pre>
                    <script>window.print(); window.close();</script>
                </body>
            </html>
        `);
        printWindow.document.close();

        this.showNotification('Impression en cours...', 'info');
    }

    showNotification(message, type = 'info', duration = 3000) {
        if (typeof showNotification === 'function') {
            showNotification(message, type, duration);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.modernPOS = new ModernPOS();
});

// Global functions for template compatibility
window.quickAdd = (productId) => window.modernPOS?.quickAdd(productId);
window.setQuantity = (value) => window.modernPOS?.setQuantity(value);
window.clearQuantity = () => window.modernPOS?.clearQuantity();
window.addToCart = () => window.modernPOS?.addToCart();
window.clearCart = () => window.modernPOS?.clearCart();
window.saveOrder = () => window.modernPOS?.saveOrder();
window.printReceipt = () => window.modernPOS?.printReceipt();
