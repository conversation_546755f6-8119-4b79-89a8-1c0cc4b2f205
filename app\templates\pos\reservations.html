{% extends "base.html" %}

{% block title %}Réservations - FuturePOS{% endblock %}

{% block content %}
<!-- Modern Reservations Interface -->
<div class="px-6 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8 animate-fade-in-up">
        <div>
            <h1 class="text-3xl font-bold text-neutral-100 mb-2">
                Réservations
                <span class="text-primary-400">Restaurant</span>
            </h1>
            <p class="text-neutral-400">
                Gestion des réservations • Aujourd'hui
            </p>
        </div>
        <div class="flex items-center gap-4">
            <div class="glass-card px-4 py-2">
                <div class="flex items-center gap-2 text-sm">
                    <div class="w-2 h-2 bg-accent-400 rounded-full animate-pulse"></div>
                    <span class="text-neutral-300">Temps réel</span>
                </div>
            </div>
            <button onclick="addNewReservation()" class="btn-primary-modern">
                <i data-lucide="plus" class="w-4 h-4"></i>
                Nouvelle réservation
            </button>
        </div>
    </div>

    <!-- Reservations Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8 stagger-children">
        <div class="glass-card p-6 hover-lift">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
                    <i data-lucide="calendar" class="w-6 h-6 text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold font-mono text-primary-400">8</div>
                    <div class="text-xs text-neutral-400">Aujourd'hui</div>
                </div>
            </div>
            <h3 class="text-sm font-medium text-neutral-300">Réservations</h3>
        </div>

        <div class="glass-card p-6 hover-lift">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold font-mono text-accent-400">6</div>
                    <div class="text-xs text-neutral-400">Confirmées</div>
                </div>
            </div>
            <h3 class="text-sm font-medium text-neutral-300">Confirmées</h3>
        </div>

        <div class="glass-card p-6 hover-lift">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center">
                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold font-mono text-yellow-400">2</div>
                    <div class="text-xs text-neutral-400">En attente</div>
                </div>
            </div>
            <h3 class="text-sm font-medium text-neutral-300">En attente</h3>
        </div>

        <div class="glass-card p-6 hover-lift">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-secondary-500 to-secondary-600 flex items-center justify-center">
                    <i data-lucide="users" class="w-6 h-6 text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold font-mono text-secondary-400">32</div>
                    <div class="text-xs text-neutral-400">Couverts</div>
                </div>
            </div>
            <h3 class="text-sm font-medium text-neutral-300">Total couverts</h3>
        </div>
    </div>

    <!-- Reservations Timeline -->
    <div class="glass-card p-6 animate-fade-in-up">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-neutral-100 flex items-center gap-2">
                <i data-lucide="clock" class="w-5 h-5 text-primary-400"></i>
                Planning du jour
            </h2>
            <div class="flex items-center gap-2">
                <button class="btn-ghost-modern text-sm">
                    <i data-lucide="calendar-days" class="w-4 h-4"></i>
                    Changer de date
                </button>
            </div>
        </div>

        <!-- Timeline -->
        <div class="space-y-4">
            <!-- Time slots -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Morning -->
                <div>
                    <h3 class="text-lg font-semibold text-neutral-200 mb-4 flex items-center gap-2">
                        <i data-lucide="sunrise" class="w-4 h-4 text-yellow-400"></i>
                        Déjeuner (12h00 - 14h30)
                    </h3>
                    <div class="space-y-3">
                        <!-- Sample reservations -->
                        <div class="reservation-card confirmed">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center">
                                        <span class="text-white font-bold text-sm">5</span>
                                    </div>
                                    <div>
                                        <div class="font-medium text-neutral-100">M. Dupont</div>
                                        <div class="text-sm text-neutral-400">12:30 • 4 personnes • Table 5</div>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-500/20 text-accent-400">
                                        Confirmée
                                    </span>
                                    <button class="quick-action-btn-modern">
                                        <i data-lucide="phone" class="w-3 h-3"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="reservation-card confirmed">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center">
                                        <span class="text-white font-bold text-sm">2</span>
                                    </div>
                                    <div>
                                        <div class="font-medium text-neutral-100">Mme Martin</div>
                                        <div class="text-sm text-neutral-400">13:00 • 2 personnes • Table 2</div>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-500/20 text-accent-400">
                                        Confirmée
                                    </span>
                                    <button class="quick-action-btn-modern">
                                        <i data-lucide="phone" class="w-3 h-3"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Evening -->
                <div>
                    <h3 class="text-lg font-semibold text-neutral-200 mb-4 flex items-center gap-2">
                        <i data-lucide="moon" class="w-4 h-4 text-primary-400"></i>
                        Dîner (19h00 - 22h30)
                    </h3>
                    <div class="space-y-3">
                        <div class="reservation-card pending">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center">
                                        <span class="text-white font-bold text-sm">7</span>
                                    </div>
                                    <div>
                                        <div class="font-medium text-neutral-100">M. Bernard</div>
                                        <div class="text-sm text-neutral-400">19:30 • 6 personnes • Table 7</div>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-400">
                                        En attente
                                    </span>
                                    <button class="quick-action-btn-modern">
                                        <i data-lucide="phone" class="w-3 h-3"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="reservation-card confirmed">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center">
                                        <span class="text-white font-bold text-sm">3</span>
                                    </div>
                                    <div>
                                        <div class="font-medium text-neutral-100">Famille Rousseau</div>
                                        <div class="text-sm text-neutral-400">20:00 • 8 personnes • Table 3</div>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-500/20 text-accent-400">
                                        Confirmée
                                    </span>
                                    <button class="quick-action-btn-modern">
                                        <i data-lucide="phone" class="w-3 h-3"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Reservation Modal -->
<div id="addReservationModal" class="payment-modal-modern">
    <div class="payment-modal-content-modern">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-neutral-100 flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
                    <i data-lucide="calendar-plus" class="w-5 h-5 text-white"></i>
                </div>
                Nouvelle réservation
            </h3>
            <button onclick="closeAddReservationModal()" class="quick-action-btn-modern">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
        </div>

        <form id="addReservationForm" class="space-y-6">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-neutral-300 mb-2">
                        <i data-lucide="user" class="w-4 h-4 inline mr-2"></i>
                        Nom du client
                    </label>
                    <input type="text" name="customerName" class="input-modern" placeholder="Nom complet" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-neutral-300 mb-2">
                        <i data-lucide="phone" class="w-4 h-4 inline mr-2"></i>
                        Téléphone
                    </label>
                    <input type="tel" name="customerPhone" class="input-modern" placeholder="06 12 34 56 78" required>
                </div>
            </div>

            <div class="grid grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-neutral-300 mb-2">
                        <i data-lucide="calendar" class="w-4 h-4 inline mr-2"></i>
                        Date
                    </label>
                    <input type="date" name="reservationDate" class="input-modern" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-neutral-300 mb-2">
                        <i data-lucide="clock" class="w-4 h-4 inline mr-2"></i>
                        Heure
                    </label>
                    <input type="time" name="reservationTime" class="input-modern" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-neutral-300 mb-2">
                        <i data-lucide="users" class="w-4 h-4 inline mr-2"></i>
                        Nombre de personnes
                    </label>
                    <input type="number" name="guestCount" class="input-modern" placeholder="4" min="1" max="20" required>
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-neutral-300 mb-2">
                    <i data-lucide="message-square" class="w-4 h-4 inline mr-2"></i>
                    Notes spéciales (optionnel)
                </label>
                <textarea name="notes" class="input-modern" rows="3" placeholder="Allergies, demandes spéciales..."></textarea>
            </div>
        </form>

        <div class="flex gap-3 mt-8">
            <button type="button" onclick="closeAddReservationModal()" class="btn-ghost-modern flex-1 py-3">
                <i data-lucide="x" class="w-4 h-4"></i>
                Annuler
            </button>
            <button type="button" onclick="submitReservation()" class="btn-primary-modern flex-1 py-3 text-lg font-semibold">
                <i data-lucide="check" class="w-4 h-4"></i>
                Créer la réservation
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addNewReservation() {
    const modal = document.getElementById('addReservationModal');
    modal.classList.add('open');

    // Set today's date as default
    const today = new Date().toISOString().split('T')[0];
    document.querySelector('input[name="reservationDate"]').value = today;
}

function closeAddReservationModal() {
    const modal = document.getElementById('addReservationModal');
    modal.classList.remove('open');
}

function submitReservation() {
    showNotification('Réservation créée avec succès !', 'success');
    closeAddReservationModal();
}

// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', () => {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>

<style>
.reservation-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: 1rem;
    transition: all var(--transition-normal);
}

.reservation-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.reservation-card.confirmed {
    border-left: 4px solid var(--accent-500);
}

.reservation-card.pending {
    border-left: 4px solid #eab308;
}

.reservation-card.cancelled {
    border-left: 4px solid #ef4444;
}
</style>
{% endblock %}