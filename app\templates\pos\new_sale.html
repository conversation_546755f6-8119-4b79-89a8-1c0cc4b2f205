{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Contenu principal -->
    <div class="grid grid-cols-12 gap-6">
        <!-- Numpad à gauche -->
        <div class="col-span-3">
            <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
                <!-- Affichage de la quantité -->
                <div class="bg-gray-50 dark:bg-gray-700 p-4 border-b border-gray-200 dark:border-gray-600">
                    <div class="text-center text-3xl font-bold text-gray-900 dark:text-white numpad-display">1</div>
                </div>
                <!-- Grille du numpad -->
                <div class="p-4 grid grid-cols-3 gap-3">
                    <button type="button" class="numpad-btn" data-value="1" onclick="handleNumpadInput('1')">1</button>
                    <button type="button" class="numpad-btn" data-value="2" onclick="handleNumpadInput('2')">2</button>
                    <button type="button" class="numpad-btn" data-value="3" onclick="handleNumpadInput('3')">3</button>
                    <button type="button" class="numpad-btn" data-value="4" onclick="handleNumpadInput('4')">4</button>
                    <button type="button" class="numpad-btn" data-value="5" onclick="handleNumpadInput('5')">5</button>
                    <button type="button" class="numpad-btn" data-value="6" onclick="handleNumpadInput('6')">6</button>
                    <button type="button" class="numpad-btn" data-value="7" onclick="handleNumpadInput('7')">7</button>
                    <button type="button" class="numpad-btn" data-value="8" onclick="handleNumpadInput('8')">8</button>
                    <button type="button" class="numpad-btn" data-value="9" onclick="handleNumpadInput('9')">9</button>
                    <button type="button" class="numpad-btn" data-value="." onclick="handleNumpadInput('.')">.</button>
                    <button type="button" class="numpad-btn" data-value="0" onclick="handleNumpadInput('0')">0</button>
                    <button type="button" class="numpad-btn bg-red-500 hover:bg-red-600 text-white" data-value="clear" onclick="handleNumpadInput('clear')">
                        <i class="fas fa-backspace"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Produits au milieu -->
        <div class="col-span-6">
            <!-- Barre de recherche et catégories -->
            <div class="mb-4 space-y-4">
                <div class="relative">
                    <input type="text" id="searchInput" class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" placeholder="Rechercher un produit...">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
                <div class="flex space-x-2 overflow-x-auto pb-2">
                    <button type="button" class="category-btn active" data-category="all">
                        Tous
                    </button>
                    {% for category in categories %}
                    <button type="button" class="category-btn" data-category="{{ category.id }}">
                        {{ category.name }}
                    </button>
                    {% endfor %}
                </div>
            </div>

            <!-- Grille des produits -->
            <div class="grid grid-cols-3 gap-4">
                {% for product in products %}
                <div class="product-card bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1" data-product-id="{{ product.id }}" data-category="{{ product.category.id }}">
                    <div class="p-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">{{ product.name }}</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">{{ product.category.name }}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-bold text-gray-900 dark:text-white">{{ product.price|round(2) }} €</span>
                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ product.stock }} {{ product.unit }}</span>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Panier à droite -->
        <div class="col-span-3">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Panier</h2>
                    <button type="button" onclick="clearCart()" class="text-sm text-red-500 hover:text-red-700">
                        <i class="fas fa-trash"></i> Vider
                    </button>
                </div>
                <div id="cartItems" class="space-y-4 mb-4 max-h-[calc(100vh-400px)] overflow-y-auto">
                    <!-- Les articles du panier seront ajoutés ici dynamiquement -->
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 pt-4 space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500 dark:text-gray-400">Sous-total</span>
                        <span id="subtotal" class="text-gray-900 dark:text-white">0.00 €</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500 dark:text-gray-400">TVA (20%)</span>
                        <span id="tax" class="text-gray-900 dark:text-white">0.00 €</span>
                    </div>
                    <div class="flex justify-between text-lg font-medium">
                        <span class="text-gray-900 dark:text-white">Total</span>
                        <span id="total" class="text-gray-900 dark:text-white">0.00 €</span>
                    </div>
                </div>
                <button type="button" onclick="showPaymentModal()" class="mt-4 w-full bg-blue-600 text-white px-4 py-3 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                    <i class="fas fa-money-bill-wave mr-2"></i> Payer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de paiement -->
<div id="paymentModal" class="hidden fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Paiement</h3>
        <form id="paymentForm">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Numéro de table</label>
                <input type="number" name="tableNumber" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Méthode de paiement</label>
                <select name="paymentMethod" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm">
                    <option value="cash">Espèces</option>
                    <option value="card">Carte</option>
                </select>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Montant</label>
                <input type="text" id="amount" readonly class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm bg-gray-100 dark:bg-gray-800 dark:text-white sm:text-sm">
            </div>
        </form>
        <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
            <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm" onclick="processPayment()">
                Valider
            </button>
            <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm" onclick="closePaymentModal()">
                Annuler
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let selectedProduct = null;
    let currentQuantity = '1';

    // Fonction pour gérer l'entrée du numpad
    function handleNumpadInput(value) {
        const display = document.querySelector('.numpad-display');
        
        if (value === 'clear') {
            currentQuantity = '1';
        } else if (value === '.') {
            if (!currentQuantity.includes('.')) {
                currentQuantity += value;
            }
        } else {
            // Si c'est le premier chiffre, remplacer le 1 par défaut
            if (currentQuantity === '1') {
                currentQuantity = value;
            } else {
                // Vérifier si le nombre ne dépasse pas 999
                const newValue = currentQuantity + value;
                if (parseFloat(newValue) <= 999) {
                    currentQuantity = newValue;
                }
            }
        }

        // Mettre à jour l'affichage
        display.textContent = currentQuantity;
    }

    // Fonction pour sélectionner un produit
    function selectProduct(card) {
        // Désélectionner tous les produits
        document.querySelectorAll('.product-card').forEach(c => {
            c.classList.remove('selected');
            c.classList.remove('ring-2', 'ring-blue-500');
        });
        
        // Sélectionner le produit cliqué
        card.classList.add('selected');
        card.classList.add('ring-2', 'ring-blue-500');
        selectedProduct = card;
        
        // Ajouter directement au panier avec la quantité actuelle
        const quantity = parseFloat(currentQuantity);
        if (quantity > 0 && quantity <= 999) {
            addToCart(card.dataset.productId, quantity);
        } else {
            showNotification('La quantité doit être comprise entre 0 et 999', 'error');
        }
    }

    // Fonction pour ajouter au panier
    function addToCart(productId, quantity) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        console.log('Adding to cart:', { productId, quantity });
        
        fetch('/pos/add_to_cart', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken
            },
            body: JSON.stringify({
                product_id: productId,
                quantity: parseFloat(quantity)
            })
        })
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                return response.text().then(text => {
                    throw new Error(`HTTP error! status: ${response.status}, body: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                updateCart(data.cart);
                showNotification('Produit ajouté au panier');
                
                // Réinitialiser la sélection
                selectedProduct = null;
                document.querySelectorAll('.product-card').forEach(c => {
                    c.classList.remove('selected');
                    c.classList.remove('ring-2', 'ring-blue-500');
                });
                currentQuantity = '1';
                document.querySelector('.numpad-display').textContent = '1';
            } else {
                showNotification(data.message || 'Erreur lors de l\'ajout au panier', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Erreur lors de l\'ajout au panier: ' + error.message, 'error');
        });
    }

    // Fonction pour mettre à jour l'affichage du panier
    function updateCart(cartData) {
        const cartItems = document.getElementById('cartItems');
        cartItems.innerHTML = '';
        
        let subtotal = 0;
        
        cartData.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-700 rounded';
            itemElement.innerHTML = `
                <div>
                    <div class="font-medium text-gray-900 dark:text-white">${item.name}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">${item.quantity} x ${item.price.toFixed(2)} €</div>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="font-medium text-gray-900 dark:text-white">${(item.price * item.quantity).toFixed(2)} €</span>
                    <button onclick="removeFromCart(${item.product_id})" class="text-red-500 hover:text-red-700">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            cartItems.appendChild(itemElement);
            subtotal += item.price * item.quantity;
        });
        
        const tax = subtotal * 0.20;
        const total = subtotal + tax;
        
        document.getElementById('subtotal').textContent = `${subtotal.toFixed(2)} €`;
        document.getElementById('tax').textContent = `${tax.toFixed(2)} €`;
        document.getElementById('total').textContent = `${total.toFixed(2)} €`;
    }

    // Fonction pour afficher les notifications
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500' : 'bg-red-500'
        } text-white`;
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Fonction pour filtrer les produits par catégorie
    document.querySelectorAll('.category-btn').forEach(button => {
        button.addEventListener('click', () => {
            document.querySelectorAll('.category-btn').forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            
            const category = button.dataset.category;
            document.querySelectorAll('.product-card').forEach(card => {
                if (category === 'all' || card.dataset.category === category) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });

    // Fonction pour rechercher des produits
    document.getElementById('searchInput').addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        document.querySelectorAll('.product-card').forEach(card => {
            const productName = card.querySelector('h3').textContent.toLowerCase();
            const categoryName = card.querySelector('p').textContent.toLowerCase();
            
            if (productName.includes(searchTerm) || categoryName.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    });

    // Fonction pour supprimer un article du panier
    function removeFromCart(productId) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        fetch(`/pos/remove_from_cart/${productId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-Token': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCart(data.cart);
                showNotification('Produit retiré du panier');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Erreur lors de la suppression du produit', 'error');
        });
    }

    // Fonction pour vider le panier
    function clearCart() {
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        fetch('/pos/clear_cart', {
            method: 'POST',
            headers: {
                'X-CSRF-Token': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCart([]);
                showNotification('Panier vidé');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Erreur lors du vidage du panier', 'error');
        });
    }

    // Fonction pour afficher le modal de paiement
    function showPaymentModal() {
        const cartItems = document.getElementById('cartItems');
        if (!cartItems.children.length) {
            showNotification('Le panier est vide', 'error');
            return;
        }
        
        const total = document.getElementById('total').textContent;
        document.getElementById('amount').value = total;
        document.getElementById('paymentModal').classList.remove('hidden');
    }

    // Fonction pour fermer le modal de paiement
    function closePaymentModal() {
        document.getElementById('paymentModal').classList.add('hidden');
    }

    // Fonction pour traiter le paiement
    function processPayment() {
        const form = document.getElementById('paymentForm');
        const formData = new FormData(form);
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        fetch('/pos/checkout', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken
            },
            body: JSON.stringify({
                table_number: formData.get('tableNumber'),
                payment_method: formData.get('paymentMethod')
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.text().then(text => {
                    throw new Error(`HTTP error! status: ${response.status}, body: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showNotification(data.message);
                window.location.href = '/pos/sales_history';
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Une erreur est survenue lors du paiement: ' + error.message, 'error');
        });
    }

    // Initialisation
    document.addEventListener('DOMContentLoaded', () => {
        console.log('Page loaded');
        
        // Ajouter les gestionnaires d'événements pour les produits
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', () => {
                console.log('Product clicked:', card.dataset.productId);
                selectProduct(card);
            });
        });

        // Vérifier que le CSRF token est présent
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
            console.error('CSRF token not found!');
        } else {
            console.log('CSRF token found:', csrfToken.getAttribute('content'));
        }
    });
</script>

<style>
    .numpad-btn {
        @apply w-full py-4 px-4 text-xl font-medium text-gray-900 dark:text-white bg-white dark:bg-gray-700 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 border border-gray-200 dark:border-gray-600;
    }
    
    .numpad-btn:active {
        @apply transform scale-95;
    }
    
    .numpad-btn[data-value="clear"] {
        @apply text-white;
    }
    
    .product-card {
        @apply cursor-pointer;
    }
    
    .product-card.selected {
        @apply ring-2 ring-blue-500;
    }

    .category-btn {
        @apply px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 whitespace-nowrap;
    }

    .category-btn.active {
        @apply bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700;
    }

    #cartItems::-webkit-scrollbar {
        width: 6px;
    }

    #cartItems::-webkit-scrollbar-track {
        @apply bg-gray-100 dark:bg-gray-700 rounded-full;
    }

    #cartItems::-webkit-scrollbar-thumb {
        @apply bg-gray-300 dark:bg-gray-600 rounded-full;
    }

    #cartItems::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-400 dark:bg-gray-500;
    }
</style>
{% endblock %} 