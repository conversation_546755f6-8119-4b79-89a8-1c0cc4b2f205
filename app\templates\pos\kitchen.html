{% extends "base.html" %}

{% block title %}Cuisine - FuturePOS{% endblock %}

{% block content %}
<!-- Modern Kitchen Interface -->
<div class="px-6 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8 animate-fade-in-up">
        <div>
            <h1 class="text-3xl font-bold text-neutral-100 mb-2">
                Interface Cuisine
                <span class="text-primary-400">Restaurant</span>
            </h1>
            <p class="text-neutral-400">
                Gestion des commandes en temps réel • <span id="activeOrders">0</span> commandes actives
            </p>
        </div>
        <div class="flex items-center gap-4">
            <div class="glass-card px-4 py-2">
                <div class="flex items-center gap-2 text-sm">
                    <div class="w-2 h-2 bg-accent-400 rounded-full animate-pulse"></div>
                    <span class="text-neutral-300">Temps réel</span>
                </div>
            </div>
            <button onclick="refreshOrders()" class="btn-secondary-modern">
                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                Actualiser
            </button>
        </div>
    </div>

    <!-- Kitchen Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8 stagger-children">
        <div class="glass-card p-6 hover-lift">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center">
                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold font-mono text-red-400" id="pendingOrders">0</div>
                    <div class="text-xs text-neutral-400">En attente</div>
                </div>
            </div>
            <h3 class="text-sm font-medium text-neutral-300">Commandes à préparer</h3>
        </div>

        <div class="glass-card p-6 hover-lift">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center">
                    <i data-lucide="chef-hat" class="w-6 h-6 text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold font-mono text-yellow-400" id="cookingOrders">0</div>
                    <div class="text-xs text-neutral-400">En cours</div>
                </div>
            </div>
            <h3 class="text-sm font-medium text-neutral-300">En préparation</h3>
        </div>

        <div class="glass-card p-6 hover-lift">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center">
                    <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold font-mono text-accent-400" id="readyOrders">0</div>
                    <div class="text-xs text-neutral-400">Prêtes</div>
                </div>
            </div>
            <h3 class="text-sm font-medium text-neutral-300">À servir</h3>
        </div>

        <div class="glass-card p-6 hover-lift">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
                    <i data-lucide="timer" class="w-6 h-6 text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold font-mono text-primary-400" id="avgTime">0min</div>
                    <div class="text-xs text-neutral-400">Temps moyen</div>
                </div>
            </div>
            <h3 class="text-sm font-medium text-neutral-300">Préparation</h3>
        </div>
    </div>

    <!-- Orders Board -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Pending Orders -->
        <div class="glass-card p-6 animate-fade-in-up">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-neutral-100 flex items-center gap-2">
                    <i data-lucide="clock" class="w-5 h-5 text-red-400"></i>
                    En attente
                </h2>
                <div class="w-6 h-6 rounded-full bg-red-500 flex items-center justify-center text-white text-xs font-bold" id="pendingCount">0</div>
            </div>
            <div id="pendingOrdersList" class="space-y-4 max-h-96 overflow-y-auto">
                <!-- Pending orders will be populated here -->
            </div>
        </div>

        <!-- Cooking Orders -->
        <div class="glass-card p-6 animate-fade-in-up" style="animation-delay: 0.1s;">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-neutral-100 flex items-center gap-2">
                    <i data-lucide="chef-hat" class="w-5 h-5 text-yellow-400"></i>
                    En préparation
                </h2>
                <div class="w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center text-white text-xs font-bold" id="cookingCount">0</div>
            </div>
            <div id="cookingOrdersList" class="space-y-4 max-h-96 overflow-y-auto">
                <!-- Cooking orders will be populated here -->
            </div>
        </div>

        <!-- Ready Orders -->
        <div class="glass-card p-6 animate-fade-in-up" style="animation-delay: 0.2s;">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-neutral-100 flex items-center gap-2">
                    <i data-lucide="check-circle" class="w-5 h-5 text-accent-400"></i>
                    Prêtes à servir
                </h2>
                <div class="w-6 h-6 rounded-full bg-accent-500 flex items-center justify-center text-white text-xs font-bold" id="readyCount">0</div>
            </div>
            <div id="readyOrdersList" class="space-y-4 max-h-96 overflow-y-auto">
                <!-- Ready orders will be populated here -->
            </div>
        </div>
    </div>
</div>

<!-- Order Detail Modal -->
<div id="orderModal" class="payment-modal-modern">
    <div class="payment-modal-content-modern max-w-3xl">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-neutral-100 flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
                    <i data-lucide="receipt" class="w-5 h-5 text-white"></i>
                </div>
                <span id="modalOrderTitle">Commande #123</span>
            </h3>
            <button onclick="closeOrderModal()" class="quick-action-btn-modern">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
        </div>

        <div id="orderModalContent">
            <!-- Content will be populated dynamically -->
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/kitchen.js') }}"></script>
{% endblock %}
