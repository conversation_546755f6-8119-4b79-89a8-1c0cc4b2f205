{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- En-tête -->
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Point de vente</h2>
        <div class="flex space-x-3">
            <a href="{{ url_for('pos.new_sale') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i> Nouvelle vente
            </a>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-3">
                    <i class="fas fa-chart-line text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div class="ml-5">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Ventes du jour</h3>
                    <p class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">{{ daily_sales.total }} €</p>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ daily_sales.count }} ventes</p>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-red-100 dark:bg-red-900 rounded-md p-3">
                    <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-xl"></i>
                </div>
                <div class="ml-5">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Stock faible</h3>
                    <p class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">{{ low_stock_count }}</p>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">produits</p>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-3">
                    <i class="fas fa-tags text-green-600 dark:text-green-400 text-xl"></i>
                </div>
                <div class="ml-5">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Catégories populaires</h3>
                    <p class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">{{ popular_categories_count }}</p>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">catégories</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <a href="{{ url_for('pos.new_sale') }}" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-3">
                    <i class="fas fa-cash-register text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div class="ml-5">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Nouvelle vente</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Créer une nouvelle vente</p>
                </div>
            </div>
        </a>
        <a href="{{ url_for('pos.cash_register') }}" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-3">
                    <i class="fas fa-cash-register text-green-600 dark:text-green-400 text-xl"></i>
                </div>
                <div class="ml-5">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Caisse</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Gérer la caisse</p>
                </div>
            </div>
        </a>
        <a href="{{ url_for('pos.z_report') }}" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-purple-100 dark:bg-purple-900 rounded-md p-3">
                    <i class="fas fa-file-invoice text-purple-600 dark:text-purple-400 text-xl"></i>
                </div>
                <div class="ml-5">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Rapport Z</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Générer un rapport Z</p>
                </div>
            </div>
        </a>
        <a href="{{ url_for('pos.cash_movements') }}" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-yellow-100 dark:bg-yellow-900 rounded-md p-3">
                    <i class="fas fa-exchange-alt text-yellow-600 dark:text-yellow-400 text-xl"></i>
                </div>
                <div class="ml-5">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Mouvements</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Voir les mouvements de caisse</p>
                </div>
            </div>
        </a>
    </div>

    <!-- Dernières ventes -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Dernières ventes</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Table</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Paiement</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {% for sale in recent_sales %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">#{{ sale.id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ sale.date }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">Table {{ sale.table_number }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ sale.total }} €</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if sale.payment_method == 'cash' %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200{% elif sale.payment_method == 'card' %}bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200{% else %}bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200{% endif %},">
                                {% if sale.payment_method == 'cash' %}
                                    Espèces
                                {% elif sale.payment_method == 'card' %}
                                    Carte
                                {% else %}
                                    Virement
                                {% endif %}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button type="button" class="text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 mr-3" data-sale-id="{{ sale.id }}" onclick="viewSaleDetails(this.dataset.saleId)">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="text-green-400 hover:text-green-500 dark:hover:text-green-300" data-sale-id="{{ sale.id }}" onclick="printReceipt(this.dataset.saleId)">
                                <i class="fas fa-print"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal des détails de vente -->
<div id="saleDetailsModal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
            <div>
                <div class="mt-3 text-center sm:mt-5">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                        Détails de la vente
                    </h3>
                    <div class="mt-4">
                        <div id="saleDetails" class="space-y-4">
                            <!-- Les détails seront chargés dynamiquement -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-5 sm:mt-6">
                <button type="button" class="w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm" onclick="closeSaleDetailsModal()">
                    Fermer
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Fonction pour afficher les détails d'une vente
    function viewSaleDetails(id) {
        fetch(`/pos/sale_details/${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const details = document.getElementById('saleDetails');
                    details.innerHTML = `
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Articles</h4>
                                <ul class="mt-2 space-y-2">
                                    ${data.sale.items.map(item => `
                                        <li class="flex justify-between text-sm">
                                            <span class="text-gray-900 dark:text-white">${item.name} x${item.quantity}</span>
                                            <span class="text-gray-900 dark:text-white">${item.total} €</span>
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                            <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500 dark:text-gray-400">Sous-total</span>
                                    <span class="text-gray-900 dark:text-white">${data.sale.subtotal} €</span>
                                </div>
                                <div class="flex justify-between text-sm mt-2">
                                    <span class="text-gray-500 dark:text-gray-400">TVA</span>
                                    <span class="text-gray-900 dark:text-white">${data.sale.tax} €</span>
                                </div>
                                <div class="flex justify-between text-base font-medium mt-4">
                                    <span class="text-gray-900 dark:text-white">Total</span>
                                    <span class="text-gray-900 dark:text-white">${data.sale.total} €</span>
                                </div>
                            </div>
                        </div>
                    `;
                    document.getElementById('saleDetailsModal').classList.remove('hidden');
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue');
            });
    }

    // Fonction pour fermer le modal des détails
    function closeSaleDetailsModal() {
        document.getElementById('saleDetailsModal').classList.add('hidden');
    }

    // Fonction pour imprimer un reçu
    function printReceipt(id) {
        window.open(`/pos/print_receipt/${id}`, '_blank');
    }
</script>
{% endblock %} 