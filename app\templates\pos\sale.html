{% extends "base.html" %}

{% block title %}Vente #{{ sale.id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <h2>Détails de la vente #{{ sale.id }}</h2>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary" onclick="window.location.href='{{ url_for('pos.history') }}'">
                    <i class="fas fa-arrow-left"></i> Retour
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="printReceipt()">
                    <i class="fas fa-print"></i> Imprimer le ticket
                </button>
                {% if sale.status == 'completed' %}
                <button type="button" class="btn btn-outline-danger" onclick="cancelSale()">
                    <i class="fas fa-times"></i> Annuler la vente
                </button>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Informations de la vente -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informations</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-4">Date</dt>
                        <dd class="col-sm-8">{{ sale.created_at.strftime('%d/%m/%Y %H:%M') }}</dd>

                        <dt class="col-sm-4">Table</dt>
                        <dd class="col-sm-8">Table {{ sale.table_number }}</dd>

                        <dt class="col-sm-4">Statut</dt>
                        <dd class="col-sm-8">
                            {% if sale.status == 'completed' %}
                                <span class="badge bg-success">Terminée</span>
                            {% else %}
                                <span class="badge bg-danger">Annulée</span>
                            {% endif %}
                        </dd>

                        <dt class="col-sm-4">Paiement</dt>
                        <dd class="col-sm-8">
                            {% if sale.payment_method == 'cash' %}
                                <span class="badge bg-success">Espèces</span>
                            {% else %}
                                <span class="badge bg-primary">Carte</span>
                            {% endif %}
                        </dd>

                        {% if sale.payment_method == 'cash' %}
                        <dt class="col-sm-4">Espèces reçus</dt>
                        <dd class="col-sm-8">{{ sale.cash_amount|format_currency }}</dd>

                        <dt class="col-sm-4">Monnaie rendue</dt>
                        <dd class="col-sm-8">{{ (sale.cash_amount - sale.total_amount)|format_currency }}</dd>
                        {% endif %}

                        <dt class="col-sm-4">Serveur</dt>
                        <dd class="col-sm-8">{{ sale.user.first_name }} {{ sale.user.last_name }}</dd>
                    </dl>
                </div>
            </div>

            <!-- Totaux -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Totaux</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-6">Sous-total</dt>
                        <dd class="col-sm-6 text-end">{{ sale.subtotal|format_currency }}</dd>

                        <dt class="col-sm-6">TVA (20%)</dt>
                        <dd class="col-sm-6 text-end">{{ sale.tax|format_currency }}</dd>

                        <dt class="col-sm-6">Total</dt>
                        <dd class="col-sm-6 text-end"><strong>{{ sale.total_amount|format_currency }}</strong></dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Articles -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Articles</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Article</th>
                                    <th>Catégorie</th>
                                    <th>Quantité</th>
                                    <th>Prix unitaire</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in sale.items %}
                                <tr>
                                    <td>{{ item.product.name }}</td>
                                    <td>{{ item.product.category.name }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ item.unit_price|format_currency }}</td>
                                    <td>{{ item.total_price|format_currency }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function printReceipt() {
        window.open(`{{ url_for('pos.receipt', id=sale.id) }}`, '_blank');
    }

    function cancelSale() {
        if (confirm('Êtes-vous sûr de vouloir annuler cette vente ? Cette action est irréversible.')) {
            fetch(`{{ url_for('pos.cancel_sale', id=sale.id) }}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Vente annulée avec succès', 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification(data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Erreur lors de l\'annulation de la vente', 'danger');
            });
        }
    }
</script>
{% endblock %} 