<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Ticket de caisse</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @media print {
            body {
                font-family: 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.4;
                margin: 0;
                padding: 10px;
            }

            .receipt {
                width: 80mm;
                margin: 0 auto;
            }

            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900">
    <button class="fixed top-5 right-5 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 no-print" onclick="window.print()">
        <i class="fas fa-print mr-2"></i>Imprimer
    </button>

    <div class="receipt bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 max-w-md mx-auto my-8">
        <!-- En-tête -->
        <div class="text-center mb-6">
            <h1 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ business.name }}</h1>
            <div class="text-sm text-gray-600 dark:text-gray-400">
                {{ business.address }}<br>
                Tél: {{ business.phone }}<br>
                {{ business.email }}
            </div>
        </div>

        <!-- Informations de vente -->
        <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div class="text-sm text-gray-600 dark:text-gray-400">
                <div class="flex justify-between mb-1">
                    <span>Ticket #</span>
                    <span class="font-medium text-gray-900 dark:text-white">{{ sale.id }}</span>
                </div>
                <div class="flex justify-between mb-1">
                    <span>Date</span>
                    <span class="font-medium text-gray-900 dark:text-white">{{ sale.created_at.strftime('%d/%m/%Y %H:%M') }}</span>
                </div>
                <div class="flex justify-between mb-1">
                    <span>Table</span>
                    <span class="font-medium text-gray-900 dark:text-white">{{ sale.table_number }}</span>
                </div>
                <div class="flex justify-between">
                    <span>Serveur</span>
                    <span class="font-medium text-gray-900 dark:text-white">{{ sale.user.first_name }} {{ sale.user.last_name }}</span>
                </div>
            </div>
        </div>

        <!-- Articles -->
        <div class="mb-6">
            {% for item in sale.items %}
            <div class="mb-4">
                <div class="flex justify-between items-start mb-1">
                    <span class="text-gray-900 dark:text-white font-medium">{{ item.product.name }}</span>
                    <span class="text-gray-900 dark:text-white font-medium">{{ item.price|format_currency }}</span>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    {{ item.quantity }} x {{ item.unit_price|format_currency }}
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Totaux -->
        <div class="mb-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex justify-between mb-2">
                <span class="text-gray-600 dark:text-gray-400">Sous-total</span>
                <span class="text-gray-900 dark:text-white">{{ sale.subtotal|format_currency }}</span>
            </div>
            <div class="flex justify-between mb-2">
                <span class="text-gray-600 dark:text-gray-400">TVA (20%)</span>
                <span class="text-gray-900 dark:text-white">{{ sale.tax|format_currency }}</span>
            </div>
            <div class="flex justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
                <span class="text-lg font-bold text-gray-900 dark:text-white">Total</span>
                <span class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ sale.total_amount|format_currency }}</span>
            </div>
        </div>

        <!-- Informations de paiement -->
        <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div class="text-sm text-gray-600 dark:text-gray-400">
                <div class="flex justify-between mb-1">
                    <span>Paiement</span>
                    <span class="font-medium text-gray-900 dark:text-white">{{ sale.payment_method|title }}</span>
                </div>
                {% if sale.payment_method == 'cash' %}
                <div class="flex justify-between mb-1">
                    <span>Reçu</span>
                    <span class="font-medium text-gray-900 dark:text-white">{{ sale.cash_amount|format_currency }}</span>
                </div>
                <div class="flex justify-between">
                    <span>Rendu</span>
                    <span class="font-medium text-gray-900 dark:text-white">{{ sale.change_amount|format_currency }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Code-barres -->
        <div class="text-center mb-6">
            <div class="text-2xl font-mono">*{{ sale.id }}*</div>
        </div>

        <!-- Pied de page -->
        <div class="text-center text-sm text-gray-600 dark:text-gray-400">
            <p class="mb-2">Merci de votre visite !</p>
            <p>{{ business.tax_number }}</p>
        </div>
    </div>

    <script>
        // Impression automatique au chargement de la page
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html> 