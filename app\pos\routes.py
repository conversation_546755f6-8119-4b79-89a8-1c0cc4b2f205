from flask import render_template, redirect, url_for, flash, request, jsonify, session
from flask_login import login_required, current_user
from flask_wtf.csrf import generate_csrf, CSRFProtect
from . import pos_bp
from app.models.models import Sale, SaleItem, Product, Category, CashMovement
from app import db
from datetime import datetime

csrf = CSRFProtect()

@pos_bp.route('/new_sale')
@login_required
def new_sale():
    # Récupérer les catégories et les produits
    categories = Category.query.filter_by(
        business_id=current_user.owned_business[0].id,
        type='product'
    ).all()
    
    products = Product.query.filter_by(
        business_id=current_user.owned_business[0].id
    ).all()
    
    return render_template('pos/new_sale.html',
                         title='Nouvelle vente',
                         categories=categories,
                         products=products,
                         csrf_token=generate_csrf())

@pos_bp.route('/add_to_cart', methods=['POST'])
@login_required
def add_to_cart():
    try:
        print("Received request to add_to_cart")
        print("Headers:", dict(request.headers))
        print("Data:", request.get_json())
        
        data = request.get_json()
        if not data:
            print("No data received")
            return jsonify({'success': False, 'message': 'Données invalides'}), 400
            
        product_id = data.get('product_id')
        quantity = float(data.get('quantity', 1))
        
        print(f"Product ID: {product_id}, Quantity: {quantity}")
        
        if not product_id:
            print("No product ID provided")
            return jsonify({'success': False, 'message': 'ID du produit manquant'}), 400
            
        product = Product.query.get_or_404(product_id)
        print(f"Found product: {product.name}")
        
        # Vérifier si le produit appartient à l'entreprise de l'utilisateur
        if product.business_id != current_user.owned_business[0].id:
            print(f"Product business_id ({product.business_id}) doesn't match user's business ({current_user.owned_business[0].id})")
            return jsonify({'success': False, 'message': 'Produit non autorisé'}), 403
        
        # Vérifier le stock
        if product.stock < quantity:
            print(f"Insufficient stock: {product.stock} < {quantity}")
            return jsonify({'success': False, 'message': 'Stock insuffisant'}), 400
        
        # Initialiser le panier dans la session si nécessaire
        if 'cart' not in session:
            print("Initializing new cart")
            session['cart'] = []
        
        # Vérifier si le produit est déjà dans le panier
        cart_item = next((item for item in session['cart'] if item['product_id'] == product_id), None)
        
        if cart_item:
            print(f"Updating existing cart item quantity: {cart_item['quantity']} -> {cart_item['quantity'] + quantity}")
            cart_item['quantity'] += quantity
        else:
            print(f"Adding new item to cart: {product.name}")
            session['cart'].append({
                'product_id': product_id,
                'name': product.name,
                'price': float(product.price),
                'quantity': quantity
            })
        
        # Marquer la session comme modifiée
        session.modified = True
        
        # Calculer le total du panier
        cart_total = sum(item['price'] * item['quantity'] for item in session['cart'])
        print(f"Cart total: {cart_total}")
        
        return jsonify({
            'success': True,
            'cart': session['cart'],
            'total': cart_total
        })
    except Exception as e:
        print(f"Error in add_to_cart: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return jsonify({'success': False, 'message': f'Une erreur est survenue: {str(e)}'}), 500

@pos_bp.route('/remove_from_cart/<int:product_id>', methods=['POST'])
@login_required
def remove_from_cart(product_id):
    if 'cart' in session:
        session['cart'] = [item for item in session['cart'] if item['product_id'] != product_id]
        session.modified = True
    
    cart_total = sum(item['price'] * item['quantity'] for item in session.get('cart', []))
    
    return jsonify({
        'success': True,
        'cart': session.get('cart', []),
        'total': cart_total
    })

@pos_bp.route('/clear_cart', methods=['POST'])
@login_required
def clear_cart():
    session.pop('cart', None)
    return jsonify({'success': True})

@pos_bp.route('/checkout', methods=['POST'])
@login_required
def checkout():
    if not session.get('cart'):
        return jsonify({'success': False, 'message': 'Le panier est vide'})
    
    data = request.get_json()
    payment_method = data.get('payment_method')
    table_number = data.get('table_number')
    
    # Créer la vente
    sale = Sale(
        business_id=current_user.owned_business[0].id,
        user_id=current_user.id,
        table_number=table_number,
        total_amount=sum(item['price'] * item['quantity'] for item in session['cart']),
        payment_method=payment_method,
        status='paid',
        paid_at=datetime.utcnow()
    )
    
    db.session.add(sale)
    
    # Ajouter les articles de la vente
    for item in session['cart']:
        product = Product.query.get(item['product_id'])
        
        # Mettre à jour le stock
        product.stock -= item['quantity']
        
        sale_item = SaleItem(
            sale=sale,
            product_id=item['product_id'],
            quantity=item['quantity'],
            price=item['price'],
            total=item['price'] * item['quantity']
        )
        
        db.session.add(sale_item)
    
    db.session.commit()
    
    # Vider le panier
    session.pop('cart', None)
    
    return jsonify({
        'success': True,
        'sale_id': sale.id,
        'message': 'Vente effectuée avec succès'
    })

@pos_bp.route('/pending_sales')
@login_required
def pending_sales():
    # Récupérer les ventes en cours
    pending_sales = Sale.query.filter_by(
        business_id=current_user.owned_business[0].id,
        status='pending'
    ).order_by(Sale.created_at.desc()).all()
    
    return render_template('pos/pending_sales.html',
                         title='Commandes en cours',
                         pending_sales=pending_sales)

@pos_bp.route('/sales_history')
@login_required
def sales_history():
    page = request.args.get('page', 1, type=int)
    per_page = 10  # Nombre d'éléments par page
    
    # Récupérer l'historique des ventes avec pagination
    pagination = Sale.query.filter_by(
        business_id=current_user.owned_business[0].id
    ).order_by(Sale.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    sales = pagination.items
    
    return render_template('pos/sales_history.html',
                         title='Historique des ventes',
                         sales=sales,
                         pagination=pagination)

@pos_bp.route('/cash_register')
@login_required
def cash_register():
    # Récupérer le solde actuel
    current_balance = db.session.query(db.func.sum(CashMovement.amount)).filter_by(
        business_id=current_user.owned_business[0].id
    ).scalar() or 0.0

    # Récupérer les entrées du jour
    today = datetime.utcnow().date()
    daily_in = db.session.query(db.func.sum(CashMovement.amount)).filter(
        CashMovement.business_id == current_user.owned_business[0].id,
        CashMovement.type == 'in',
        db.func.date(CashMovement.created_at) == today
    ).scalar() or 0.0

    # Récupérer les sorties du jour
    daily_out = db.session.query(db.func.sum(CashMovement.amount)).filter(
        CashMovement.business_id == current_user.owned_business[0].id,
        CashMovement.type == 'out',
        db.func.date(CashMovement.created_at) == today
    ).scalar() or 0.0

    # Récupérer les derniers mouvements
    movements = CashMovement.query.filter_by(
        business_id=current_user.owned_business[0].id
    ).order_by(CashMovement.created_at.desc()).limit(10).all()

    return render_template('pos/cash_register.html',
                         title='Fond de caisse',
                         current_balance=current_balance,
                         daily_in=daily_in,
                         daily_out=daily_out,
                         movements=movements)

@pos_bp.route('/cash_movements')
@login_required
def cash_movements():
    # Récupérer le numéro de page
    page = request.args.get('page', 1, type=int)
    per_page = 10  # Nombre d'éléments par page
    
    # Récupérer les mouvements de caisse avec pagination
    pagination = CashMovement.query.filter_by(
        business_id=current_user.owned_business[0].id
    ).order_by(CashMovement.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    movements = pagination.items
    
    return render_template('pos/cash_movements.html',
                         title='Mouvements de caisse',
                         movements=movements,
                         pagination=pagination)

@pos_bp.route('/z_report')
@login_required
def z_report():
    # Récupérer les ventes du jour pour l'entreprise courante
    today = datetime.utcnow().date()
    sales = Sale.query.filter(
        Sale.business_id == current_user.owned_business[0].id,
        db.func.date(Sale.created_at) == today
    ).all()
    
    # Calculer les totaux
    total_sales = sum(sale.total_amount for sale in sales)
    number_of_sales = len(sales)
    average_sale = total_sales / number_of_sales if number_of_sales > 0 else 0
    
    # Calculer les totaux par méthode de paiement
    payment_methods = {}
    for sale in sales:
        method = sale.payment_method
        if method not in payment_methods:
            payment_methods[method] = 0
        payment_methods[method] += sale.total_amount
    
    return render_template('pos/z_report.html',
                         title='Ticket Z',
                         total_sales=total_sales,
                         number_of_sales=number_of_sales,
                         average_sale=average_sale,
                         payment_methods=payment_methods) 