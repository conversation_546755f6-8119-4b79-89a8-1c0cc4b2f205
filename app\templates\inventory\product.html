{% extends "base.html" %}

{% block title %}{{ product.name }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 product-detail">
    <!-- En-tête avec actions -->
    <div class="flex justify-between items-center mb-6">
        <div class="flex items-center space-x-4">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">{{ product.name }}</h2>
            <span class="badge
                {% if product.stock > product.min_stock %}
                    bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                {% elif product.stock > 0 %}
                    bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                {% else %}
                    bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                {% endif %}">
                {% if product.stock > product.min_stock %}
                    En stock
                {% elif product.stock > 0 %}
                    Stock bas
                {% else %}
                    Épuisé
                {% endif %}
            </span>
        </div>
        <div class="flex space-x-3">
            <a href="{{ url_for('inventory.edit_product', product_id=product.id) }}" 
               class="btn inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                <i class="fas fa-edit mr-2"></i> Modifier
            </a>
            <a href="{{ url_for('inventory.products') }}" 
               class="btn inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i> Retour
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Informations principales -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Carte des informations -->
            <div class="card bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden transform transition-all duration-200 hover:shadow-xl">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                        Informations du produit
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg hover-scale">
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Catégorie</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white font-medium">{{ product.category.name }}</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg hover-scale">
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Prix de vente</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white font-medium">{{ product.price|format_currency }}</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg hover-scale">
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Stock actuel</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white font-medium">{{ product.stock }} {{ product.unit }}</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg hover-scale">
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Stock minimum</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white font-medium">{{ product.min_stock }} {{ product.unit }}</p>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg hover-scale">
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Description</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ product.description or 'Aucune description' }}</p>
                    </div>
                </div>
            </div>

            <!-- Carte des mouvements de stock -->
            <div class="card bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden transform transition-all duration-200 hover:shadow-xl">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-exchange-alt mr-2 text-blue-500"></i>
                        Mouvements de stock
                    </h3>
                </div>
                <div class="table-container overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Quantité</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Stock après</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            {% for movement in stock_movements %}
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    {{ movement.created_at.strftime('%d/%m/%Y %H:%M') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="badge
                                        {% if movement.type == 'in' %}
                                            bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                        {% else %}
                                            bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                        {% endif %}">
                                        <i class="fas {% if movement.type == 'in' %}fa-arrow-down{% else %}fa-arrow-up{% endif %} mr-1"></i>
                                        {{ movement.type|title }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    {{ movement.quantity }} {{ product.unit }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    {{ movement.stock_after }} {{ product.unit }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Statistiques -->
        <div class="space-y-6">
            <!-- Carte des statistiques -->
            <div class="card bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden transform transition-all duration-200 hover:shadow-xl">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-chart-line mr-2 text-blue-500"></i>
                        Statistiques
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="stat-card bg-blue-50 dark:bg-blue-900 rounded-lg p-4 transform transition-all duration-200 hover:scale-105">
                        <div class="flex items-center justify-between">
                            <div class="text-sm font-medium text-blue-800 dark:text-blue-200">Ventes du mois</div>
                            <i class="fas fa-shopping-cart text-blue-500 dark:text-blue-400"></i>
                        </div>
                        <div class="mt-1 text-2xl font-semibold text-blue-900 dark:text-blue-100">
                            {{ monthly_sales|format_currency }}
                        </div>
                    </div>
                    <div class="stat-card bg-green-50 dark:bg-green-900 rounded-lg p-4 transform transition-all duration-200 hover:scale-105">
                        <div class="flex items-center justify-between">
                            <div class="text-sm font-medium text-green-800 dark:text-green-200">Quantité vendue</div>
                            <i class="fas fa-box text-green-500 dark:text-green-400"></i>
                        </div>
                        <div class="mt-1 text-2xl font-semibold text-green-900 dark:text-green-100">
                            {{ monthly_quantity }} {{ product.unit }}
                        </div>
                    </div>
                    <div class="stat-card bg-yellow-50 dark:bg-yellow-900 rounded-lg p-4 transform transition-all duration-200 hover:scale-105">
                        <div class="flex items-center justify-between">
                            <div class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Rotation du stock</div>
                            <i class="fas fa-sync text-yellow-500 dark:text-yellow-400"></i>
                        </div>
                        <div class="mt-1 text-2xl font-semibold text-yellow-900 dark:text-yellow-100">
                            {{ stock_rotation }} jours
                        </div>
                    </div>
                </div>
            </div>

            <!-- Carte des alertes -->
            <div class="card bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden transform transition-all duration-200 hover:shadow-xl">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-bell mr-2 text-blue-500"></i>
                        Alertes
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    {% if product.stock <= product.min_stock %}
                    <div class="alert-card bg-red-50 dark:bg-red-900 rounded-lg p-4 transform transition-all duration-200 hover:scale-105">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-500 dark:text-red-400 mr-2"></i>
                            <span class="text-sm font-medium text-red-800 dark:text-red-200">
                                Stock bas ({{ product.stock }} {{ product.unit }})
                            </span>
                        </div>
                    </div>
                    {% endif %}
                    {% if product.stock == 0 %}
                    <div class="alert-card bg-red-50 dark:bg-red-900 rounded-lg p-4 transform transition-all duration-200 hover:scale-105">
                        <div class="flex items-center">
                            <i class="fas fa-times-circle text-red-500 dark:text-red-400 mr-2"></i>
                            <span class="text-sm font-medium text-red-800 dark:text-red-200">
                                Stock épuisé
                            </span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 