{% extends "base.html" %}

{% block title %}Catégories{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- En-tête avec bouton d'ajout -->
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Catégories</h2>
        <a href="{{ url_for('inventory.add_category') }}" 
           class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-plus mr-2"></i>
            Nouvelle catégorie
        </a>
    </div>

    <!-- Grille des catégories -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for category in categories %}
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden hover:shadow-xl transition-shadow duration-200">
            <!-- En-tête de la carte -->
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ category.name }}</h3>
                    <span class="px-2.5 py-0.5 rounded-full text-xs font-medium 
                        {% if category.type == 'product' %}
                            bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                        {% else %}
                            bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                        {% endif %}">
                        {{ category.type|title }}
                    </span>
                </div>
            </div>

            <!-- Contenu de la carte -->
            <div class="px-6 py-4">
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    {{ category.description or 'Aucune description' }}
                </p>
                
                <!-- Statistiques -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                        <div class="text-sm text-gray-500 dark:text-gray-400">Produits</div>
                        <div class="text-lg font-semibold text-gray-900 dark:text-white">
                            {{ category.products|length }}
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                        <div class="text-sm text-gray-500 dark:text-gray-400">Sous-catégories</div>
                        <div class="text-lg font-semibold text-gray-900 dark:text-white">
                            {{ category.children|length }}
                        </div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ url_for('inventory.edit_category', category_id=category.id) }}" 
                       class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-edit mr-1.5"></i>
                        Modifier
                    </a>
                    <button onclick="deleteCategory({{ category.id }})"
                            class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                        <i class="fas fa-trash mr-1.5"></i>
                        Supprimer
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div id="deleteModal" class="hidden fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Confirmer la suppression</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Êtes-vous sûr de vouloir supprimer cette catégorie ? Cette action est irréversible.
        </p>
        <div class="flex justify-end space-x-3">
            <button onclick="closeDeleteModal()" 
                    class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                Annuler
            </button>
            <button onclick="confirmDelete()" 
                    class="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                Supprimer
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let categoryToDelete = null;

    function deleteCategory(categoryId) {
        categoryToDelete = categoryId;
        document.getElementById('deleteModal').classList.remove('hidden');
    }

    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
        categoryToDelete = null;
    }

    function confirmDelete() {
        if (!categoryToDelete) return;

        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        fetch(`/inventory/delete_category/${categoryToDelete}`, {
            method: 'POST',
            headers: {
                'X-CSRF-Token': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || 'Une erreur est survenue');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue lors de la suppression');
        })
        .finally(() => {
            closeDeleteModal();
        });
    }
</script>
{% endblock %} 