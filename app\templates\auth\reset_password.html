{% extends "base.html" %}

{% block title %}Réinitialisation du mot de passe{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
                Réinitialisation du mot de passe
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
                Choisissez votre nouveau mot de passe
            </p>
        </div>
        <div class="mt-8 bg-white dark:bg-gray-800 py-8 px-4 shadow-xl rounded-lg sm:px-10">
            <form class="space-y-6" method="POST" action="">
                {{ form.hidden_tag() }}
                <div>
                    {{ form.password.label(class="block text-sm font-medium text-gray-700 dark:text-gray-300") }}
                    <div class="mt-1">
                        {{ form.password(class="appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm") }}
                        {% for error in form.password.errors %}
                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ error }}</p>
                        {% endfor %}
                    </div>
                </div>

                <div>
                    {{ form.password2.label(class="block text-sm font-medium text-gray-700 dark:text-gray-300") }}
                    <div class="mt-1">
                        {{ form.password2(class="appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm") }}
                        {% for error in form.password2.errors %}
                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ error }}</p>
                        {% endfor %}
                    </div>
                </div>

                <div>
                    {{ form.submit(class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200") }}
                </div>
            </form>

            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    Retour à la 
                    <a href="{{ url_for('auth.login') }}" class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
                        connexion
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %} 