{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- En-tête -->
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Recettes</h2>
        <div class="flex space-x-3">
            <a href="{{ url_for('inventory.add_recipe') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i> Nouvelle recette
            </a>
        </div>
    </div>

    <!-- Liste des recettes -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Nom</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Description</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Ingrédients</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Coût</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {% for recipe in recipes %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            {{ recipe.name }}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                            {{ recipe.description }}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                            {{ recipe.ingredients|length }} ingrédients
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {{ recipe.cost|round(2) }} €
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button type="button" class="text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 mr-3" data-recipe-id="{{ recipe.id }}" onclick="viewRecipe(this.dataset.recipeId)">
                                <i class="fas fa-eye"></i>
                            </button>
                            <a href="{{ url_for('inventory.edit_recipe', recipe_id=recipe.id) }}" class="text-yellow-400 hover:text-yellow-500 dark:hover:text-yellow-300 mr-3">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" class="text-red-400 hover:text-red-500 dark:hover:text-red-300" data-recipe-id="{{ recipe.id }}" onclick="deleteRecipe(this.dataset.recipeId)">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal des détails de recette -->
<div id="recipeDetailsModal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
            <div>
                <div class="mt-3 text-center sm:mt-5">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                        Détails de la recette
                    </h3>
                    <div class="mt-4">
                        <div id="recipeDetails" class="space-y-4">
                            <!-- Les détails seront chargés dynamiquement -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-5 sm:mt-6">
                <button type="button" class="w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm" onclick="closeRecipeDetailsModal()">
                    Fermer
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Fonction pour afficher les détails d'une recette
    function viewRecipe(id) {
        fetch(`/inventory/recipe/${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const details = document.getElementById('recipeDetails');
                    details.innerHTML = `
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</h4>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white">${data.recipe.description}</p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Ingrédients</h4>
                                <ul class="mt-2 space-y-2">
                                    ${data.recipe.ingredients.map(ingredient => `
                                        <li class="flex justify-between text-sm">
                                            <span class="text-gray-900 dark:text-white">${ingredient.name}</span>
                                            <span class="text-gray-900 dark:text-white">${ingredient.quantity} ${ingredient.unit}</span>
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                            <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                                <div class="flex justify-between text-base font-medium">
                                    <span class="text-gray-900 dark:text-white">Coût total</span>
                                    <span class="text-gray-900 dark:text-white">${data.recipe.cost.toFixed(2)} €</span>
                                </div>
                            </div>
                        </div>
                    `;
                    document.getElementById('recipeDetailsModal').classList.remove('hidden');
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue');
            });
    }

    // Fonction pour fermer le modal des détails
    function closeRecipeDetailsModal() {
        document.getElementById('recipeDetailsModal').classList.add('hidden');
    }

    // Fonction pour supprimer une recette
    function deleteRecipe(id) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette recette ?')) {
            fetch(`/inventory/delete_recipe/${id}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue');
            });
        }
    }
</script>
{% endblock %} 