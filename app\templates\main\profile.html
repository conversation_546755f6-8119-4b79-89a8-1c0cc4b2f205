{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="max-w-3xl mx-auto">
        <!-- Profil utilisateur -->
        <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Profil utilisateur</h3>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-24 h-24 rounded-full bg-gray-100 dark:bg-gray-700">
                            <i class="fas fa-user fa-3x text-gray-400 dark:text-gray-500"></i>
                        </div>
                        <h4 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">{{ current_user.username }}</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ current_user.role }}</p>
                    </div>
                    <div class="md:col-span-2">
                        <form method="POST" action="{{ url_for('main.profile') }}" class="space-y-4">
                            {{ form.hidden_tag() }}
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ form.username.label }}</label>
                                {{ form.username(class="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm") }}
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ form.email.label }}</label>
                                {{ form.email(class="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm") }}
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ form.current_password.label }}</label>
                                {{ form.current_password(class="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm") }}
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ form.new_password.label }}</label>
                                {{ form.new_password(class="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm") }}
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ form.confirm_password.label }}</label>
                                {{ form.confirm_password(class="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm") }}
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Rôle</label>
                                <input type="text" class="mt-1 block w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-500 dark:text-gray-400 sm:text-sm" value="{{ current_user.role }}" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Date d'inscription</label>
                                <input type="text" class="mt-1 block w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-500 dark:text-gray-400 sm:text-sm" value="{{ current_user.created_at.strftime('%d/%m/%Y') }}" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Entreprise</label>
                                <input type="text" class="mt-1 block w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-500 dark:text-gray-400 sm:text-sm" value="{{ current_user.owned_business[0].name if current_user.owned_business else 'Aucune' }}" readonly>
                            </div>
                            <div class="pt-4">
                                {{ form.submit(class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200") }}
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistiques de l'utilisateur -->
        <div class="mt-8 bg-white dark:bg-gray-800 shadow-xl rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Statistiques</h3>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <h4 class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ total_sales|default(0) }}</h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Ventes totales</p>
                    </div>
                    <div class="text-center">
                        <h4 class="text-2xl font-bold text-green-600 dark:text-green-400">{{ total_amount|default(0)|round(2) }} €</h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Montant total des ventes</p>
                    </div>
                    <div class="text-center">
                        <h4 class="text-2xl font-bold text-indigo-600 dark:text-indigo-400">{{ average_sale|default(0)|round(2) }} €</h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Moyenne par vente</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 