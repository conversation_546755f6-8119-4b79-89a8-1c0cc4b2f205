from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField
from wtforms.validators import DataRequired
from . import inventory_bp
from app.models.models import Product, Category, StockMovement, Ingredient, Recipe
from app import db
from datetime import datetime

class CategoryForm(FlaskForm):
    name = StringField('Nom', validators=[DataRequired()])
    type = SelectField('Type', choices=[
        ('product', 'Produit'),
        ('ingredient', 'Ingrédient'),
        ('charge', 'Charge')
    ], validators=[DataRequired()])
    description = TextAreaField('Description')

class ProductForm(FlaskForm):
    name = StringField('Nom', validators=[DataRequired()])
    description = TextAreaField('Description')
    price = StringField('Prix', validators=[DataRequired()])
    stock = StringField('Stock', validators=[DataRequired()])
    min_stock = StringField('Stock minimum')
    unit = StringField('Unité', validators=[DataRequired()])
    category_id = SelectField('Catégorie', coerce=int, validators=[DataRequired()])

@inventory_bp.route('/products/')
@login_required
def products_with_slash():
    return redirect(url_for('inventory.products'))

@inventory_bp.route('/products')
@login_required
def products():
    # Récupérer les produits de l'entreprise avec pagination
    page = request.args.get('page', 1, type=int)
    per_page = 12  # Nombre de produits par page
    
    products = Product.query.filter_by(
        business_id=current_user.owned_business[0].id
    ).order_by(Product.name).paginate(page=page, per_page=per_page, error_out=False)
    
    # Récupérer les catégories pour le filtre
    categories = Category.query.filter_by(
        business_id=current_user.owned_business[0].id,
        type='product'
    ).all()
    
    return render_template('inventory/products.html',
                         title='Produits',
                         products=products.items,
                         categories=categories,
                         pagination=products)

@inventory_bp.route('/product/<int:product_id>')
@login_required
def product_details(product_id):
    product = Product.query.get_or_404(product_id)
    
    # Vérifier si le produit appartient à l'entreprise de l'utilisateur
    if product.business_id != current_user.owned_business[0].id:
        flash('Produit non autorisé', 'danger')
        return redirect(url_for('inventory.products'))
    
    # Récupérer l'historique des mouvements de stock
    stock_movements = StockMovement.query.filter_by(
        product_id=product_id
    ).order_by(StockMovement.date.desc()).all()
    
    return render_template('inventory/product_details.html',
                         title=f'Détails - {product.name}',
                         product=product,
                         stock_movements=stock_movements)

@inventory_bp.route('/low_stock')
@login_required
def low_stock():
    # Récupérer les produits avec un stock inférieur au minimum
    low_stock_products = Product.query.filter(
        Product.business_id == current_user.owned_business[0].id,
        Product.stock <= Product.min_stock
    ).order_by(Product.name).all()
    
    return render_template('inventory/low_stock.html',
                         title='Stock bas',
                         products=low_stock_products)

@inventory_bp.route('/add_product', methods=['GET', 'POST'])
@login_required
def add_product():
    form = ProductForm()
    
    # Récupérer les catégories pour le formulaire
    categories = Category.query.filter_by(
        business_id=current_user.owned_business[0].id,
        type='product'
    ).all()
    form.category_id.choices = [(c.id, c.name) for c in categories]
    
    if form.validate_on_submit():
        # Créer le produit
        product = Product(
            name=form.name.data,
            description=form.description.data,
            price=float(form.price.data),
            stock=float(form.stock.data),
            min_stock=float(form.min_stock.data or 0),
            unit=form.unit.data,
            category_id=form.category_id.data,
            business_id=current_user.owned_business[0].id
        )
        
        db.session.add(product)
        
        # Créer le mouvement de stock initial
        if float(form.stock.data) > 0:
            movement = StockMovement(
                product=product,
                type='in',
                quantity=float(form.stock.data),
                description='Stock initial',
                user_id=current_user.id
            )
            db.session.add(movement)
        
        db.session.commit()
        
        flash('Produit ajouté avec succès', 'success')
        return redirect(url_for('inventory.products'))
    
    return render_template('inventory/add_product.html',
                         title='Ajouter un produit',
                         form=form,
                         categories=categories)

@inventory_bp.route('/edit_product/<int:product_id>', methods=['GET', 'POST'])
@login_required
def edit_product(product_id):
    product = Product.query.get_or_404(product_id)
    
    # Vérifier si le produit appartient à l'entreprise de l'utilisateur
    if product.business_id != current_user.owned_business[0].id:
        flash('Produit non autorisé', 'danger')
        return redirect(url_for('inventory.products'))
    
    if request.method == 'POST':
        # Mettre à jour les données du produit
        product.name = request.form.get('name')
        product.description = request.form.get('description')
        product.price = float(request.form.get('price'))
        product.min_stock = float(request.form.get('min_stock', 0))
        product.unit = request.form.get('unit')
        product.category_id = request.form.get('category_id')
        
        db.session.commit()
        
        flash('Produit modifié avec succès', 'success')
        return redirect(url_for('inventory.products'))
    
    # Récupérer les catégories pour le formulaire
    categories = Category.query.filter_by(
        business_id=current_user.owned_business[0].id,
        type='product'
    ).all()
    
    return render_template('inventory/edit_product.html',
                         title=f'Modifier - {product.name}',
                         product=product,
                         categories=categories)

@inventory_bp.route('/delete_product/<int:product_id>', methods=['POST'])
@login_required
def delete_product(product_id):
    product = Product.query.get_or_404(product_id)
    
    # Vérifier si le produit appartient à l'entreprise de l'utilisateur
    if product.business_id != current_user.owned_business[0].id:
        return jsonify({'success': False, 'message': 'Produit non autorisé'})
    
    db.session.delete(product)
    db.session.commit()
    
    return jsonify({'success': True, 'message': 'Produit supprimé avec succès'})

@inventory_bp.route('/stock_movement/<int:product_id>', methods=['POST'])
@login_required
def stock_movement(product_id):
    product = Product.query.get_or_404(product_id)
    
    # Vérifier si le produit appartient à l'entreprise de l'utilisateur
    if product.business_id != current_user.owned_business[0].id:
        return jsonify({'success': False, 'message': 'Produit non autorisé'})
    
    data = request.get_json()
    movement_type = data.get('type')
    quantity = float(data.get('quantity'))
    description = data.get('description')
    
    # Vérifier la quantité pour les sorties
    if movement_type == 'out' and product.stock < quantity:
        return jsonify({'success': False, 'message': 'Stock insuffisant'})
    
    # Créer le mouvement de stock
    movement = StockMovement(
        product=product,
        type=movement_type,
        quantity=quantity,
        description=description,
        user_id=current_user.id
    )
    
    # Mettre à jour le stock
    if movement_type == 'in':
        product.stock += quantity
    else:
        product.stock -= quantity
    
    db.session.add(movement)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': 'Mouvement de stock enregistré avec succès',
        'new_stock': product.stock
    })

@inventory_bp.route('/categories')
@login_required
def categories():
    # Récupérer les catégories de l'entreprise
    categories = Category.query.filter_by(
        business_id=current_user.owned_business[0].id,
        type='product'
    ).order_by(Category.name).all()
    
    return render_template('inventory/categories.html',
                         title='Catégories',
                         categories=categories)

@inventory_bp.route('/add_category', methods=['GET', 'POST'])
@login_required
def add_category():
    form = CategoryForm()
    if form.validate_on_submit():
        # Créer la catégorie
        category = Category(
            name=form.name.data,
            description=form.description.data,
            type=form.type.data,
            business_id=current_user.owned_business[0].id
        )
        
        db.session.add(category)
        db.session.commit()
        
        flash('Catégorie ajoutée avec succès', 'success')
        return redirect(url_for('inventory.categories'))
    
    return render_template('inventory/add_category.html',
                         title='Ajouter une catégorie',
                         form=form)

@inventory_bp.route('/edit_category/<int:category_id>', methods=['GET', 'POST'])
@login_required
def edit_category(category_id):
    category = Category.query.get_or_404(category_id)
    
    # Vérifier si la catégorie appartient à l'entreprise de l'utilisateur
    if category.business_id != current_user.owned_business[0].id:
        flash('Catégorie non autorisée', 'danger')
        return redirect(url_for('inventory.categories'))
    
    if request.method == 'POST':
        # Mettre à jour les données de la catégorie
        category.name = request.form.get('name')
        
        db.session.commit()
        
        flash('Catégorie modifiée avec succès', 'success')
        return redirect(url_for('inventory.categories'))
    
    return render_template('inventory/edit_category.html',
                         title=f'Modifier - {category.name}',
                         category=category)

@inventory_bp.route('/delete_category/<int:category_id>', methods=['POST'])
@login_required
def delete_category(category_id):
    category = Category.query.get_or_404(category_id)
    
    # Vérifier si la catégorie appartient à l'entreprise de l'utilisateur
    if category.business_id != current_user.owned_business[0].id:
        return jsonify({'success': False, 'message': 'Catégorie non autorisée'})
    
    # Vérifier si la catégorie contient des produits
    if category.products:
        return jsonify({'success': False, 'message': 'Impossible de supprimer une catégorie contenant des produits'})
    
    db.session.delete(category)
    db.session.commit()
    
    return jsonify({'success': True, 'message': 'Catégorie supprimée avec succès'})

@inventory_bp.route('/ingredients')
@login_required
def ingredients():
    # Récupérer les ingrédients de l'entreprise
    ingredients = Ingredient.query.filter_by(
        business_id=current_user.owned_business[0].id
    ).order_by(Ingredient.name).all()
    
    # Récupérer les catégories pour le filtre
    categories = Category.query.filter_by(
        business_id=current_user.owned_business[0].id,
        type='ingredient'
    ).all()
    
    return render_template('inventory/ingredients.html',
                         title='Ingrédients',
                         ingredients=ingredients,
                         categories=categories)

@inventory_bp.route('/add_ingredient', methods=['GET', 'POST'])
@login_required
def add_ingredient():
    if request.method == 'POST':
        # Récupérer les données du formulaire
        name = request.form.get('name')
        price = float(request.form.get('price'))
        stock = float(request.form.get('stock', 0))
        unit = request.form.get('unit')
        min_stock = float(request.form.get('min_stock', 0))
        category_id = request.form.get('category_id')
        expiry_date = request.form.get('expiry_date')
        
        # Créer l'ingrédient
        ingredient = Ingredient(
            name=name,
            price=price,
            stock=stock,
            unit=unit,
            min_stock=min_stock,
            category_id=category_id,
            expiry_date=datetime.strptime(expiry_date, '%Y-%m-%d') if expiry_date else None,
            business_id=current_user.owned_business[0].id
        )
        
        db.session.add(ingredient)
        db.session.commit()
        
        flash('Ingrédient ajouté avec succès', 'success')
        return redirect(url_for('inventory.ingredients'))
    
    # Récupérer les catégories pour le formulaire
    categories = Category.query.filter_by(
        business_id=current_user.owned_business[0].id,
        type='ingredient'
    ).all()
    
    return render_template('inventory/add_ingredient.html',
                         title='Ajouter un ingrédient',
                         categories=categories)

@inventory_bp.route('/edit_ingredient/<int:ingredient_id>', methods=['GET', 'POST'])
@login_required
def edit_ingredient(ingredient_id):
    ingredient = Ingredient.query.get_or_404(ingredient_id)
    
    # Vérifier si l'ingrédient appartient à l'entreprise de l'utilisateur
    if ingredient.business_id != current_user.owned_business[0].id:
        flash('Ingrédient non autorisé', 'danger')
        return redirect(url_for('inventory.ingredients'))
    
    if request.method == 'POST':
        # Mettre à jour les données de l'ingrédient
        ingredient.name = request.form.get('name')
        ingredient.price = float(request.form.get('price'))
        ingredient.unit = request.form.get('unit')
        ingredient.min_stock = float(request.form.get('min_stock', 0))
        ingredient.category_id = request.form.get('category_id')
        expiry_date = request.form.get('expiry_date')
        ingredient.expiry_date = datetime.strptime(expiry_date, '%Y-%m-%d') if expiry_date else None
        
        db.session.commit()
        
        flash('Ingrédient modifié avec succès', 'success')
        return redirect(url_for('inventory.ingredients'))
    
    # Récupérer les catégories pour le formulaire
    categories = Category.query.filter_by(
        business_id=current_user.owned_business[0].id,
        type='ingredient'
    ).all()
    
    return render_template('inventory/edit_ingredient.html',
                         title=f'Modifier - {ingredient.name}',
                         ingredient=ingredient,
                         categories=categories)

@inventory_bp.route('/delete_ingredient/<int:ingredient_id>', methods=['POST'])
@login_required
def delete_ingredient(ingredient_id):
    ingredient = Ingredient.query.get_or_404(ingredient_id)
    
    # Vérifier si l'ingrédient appartient à l'entreprise de l'utilisateur
    if ingredient.business_id != current_user.owned_business[0].id:
        return jsonify({'success': False, 'message': 'Ingrédient non autorisé'})
    
    # Vérifier si l'ingrédient est utilisé dans des recettes
    if ingredient.recipes:
        return jsonify({'success': False, 'message': 'Impossible de supprimer un ingrédient utilisé dans des recettes'})
    
    db.session.delete(ingredient)
    db.session.commit()
    
    return jsonify({'success': True, 'message': 'Ingrédient supprimé avec succès'})

@inventory_bp.route('/recipes/')
@inventory_bp.route('/recipes')
@login_required
def recipes():
    # Récupérer les recettes de l'entreprise
    recipes = Recipe.query.filter_by(
        business_id=current_user.owned_business[0].id
    ).order_by(Recipe.name).all()
    
    return render_template('inventory/recipes.html',
                         title='Recettes',
                         recipes=recipes)

@inventory_bp.route('/add_recipe', methods=['GET', 'POST'])
@login_required
def add_recipe():
    if request.method == 'POST':
        # Récupérer les données du formulaire
        name = request.form.get('name')
        description = request.form.get('description')
        
        # Créer la recette
        recipe = Recipe(
            name=name,
            description=description,
            business_id=current_user.owned_business[0].id
        )
        
        # Récupérer les ingrédients et leurs quantités
        ingredients_data = request.form.getlist('ingredients[]')
        quantities = request.form.getlist('quantities[]')
        units = request.form.getlist('units[]')
        
        # Ajouter les ingrédients à la recette
        for i in range(len(ingredients_data)):
            ingredient_id = int(ingredients_data[i])
            quantity = float(quantities[i])
            unit = units[i]
            
            # Vérifier si l'ingrédient existe
            ingredient = Ingredient.query.get(ingredient_id)
            if ingredient and ingredient.business_id == current_user.owned_business[0].id:
                recipe.ingredients.append(ingredient)
                # Mettre à jour la quantité et l'unité dans la table d'association
                recipe_ingredients.update().where(
                    db.and_(
                        recipe_ingredients.c.recipe_id == recipe.id,
                        recipe_ingredients.c.ingredient_id == ingredient_id
                    )
                ).values(quantity=quantity, unit=unit)
        
        db.session.add(recipe)
        db.session.commit()
        
        flash('Recette ajoutée avec succès', 'success')
        return redirect(url_for('inventory.recipes'))
    
    # Récupérer les ingrédients pour le formulaire
    ingredients = Ingredient.query.filter_by(
        business_id=current_user.owned_business[0].id
    ).order_by(Ingredient.name).all()
    
    return render_template('inventory/add_recipe.html',
                         title='Ajouter une recette',
                         ingredients=ingredients)

@inventory_bp.route('/edit_recipe/<int:recipe_id>', methods=['GET', 'POST'])
@login_required
def edit_recipe(recipe_id):
    recipe = Recipe.query.get_or_404(recipe_id)
    
    # Vérifier si la recette appartient à l'entreprise de l'utilisateur
    if recipe.business_id != current_user.owned_business[0].id:
        flash('Recette non autorisée', 'danger')
        return redirect(url_for('inventory.recipes'))
    
    if request.method == 'POST':
        # Mettre à jour les données de la recette
        recipe.name = request.form.get('name')
        recipe.description = request.form.get('description')
        
        # Récupérer les ingrédients et leurs quantités
        ingredients_data = request.form.getlist('ingredients[]')
        quantities = request.form.getlist('quantities[]')
        units = request.form.getlist('units[]')
        
        # Supprimer tous les ingrédients existants
        recipe.ingredients = []
        
        # Ajouter les nouveaux ingrédients
        for i in range(len(ingredients_data)):
            ingredient_id = int(ingredients_data[i])
            quantity = float(quantities[i])
            unit = units[i]
            
            # Vérifier si l'ingrédient existe
            ingredient = Ingredient.query.get(ingredient_id)
            if ingredient and ingredient.business_id == current_user.owned_business[0].id:
                recipe.ingredients.append(ingredient)
                # Mettre à jour la quantité et l'unité dans la table d'association
                recipe_ingredients.update().where(
                    db.and_(
                        recipe_ingredients.c.recipe_id == recipe.id,
                        recipe_ingredients.c.ingredient_id == ingredient_id
                    )
                ).values(quantity=quantity, unit=unit)
        
        db.session.commit()
        
        flash('Recette modifiée avec succès', 'success')
        return redirect(url_for('inventory.recipes'))
    
    # Récupérer les ingrédients pour le formulaire
    ingredients = Ingredient.query.filter_by(
        business_id=current_user.owned_business[0].id
    ).order_by(Ingredient.name).all()
    
    return render_template('inventory/edit_recipe.html',
                         title=f'Modifier - {recipe.name}',
                         recipe=recipe,
                         ingredients=ingredients)

@inventory_bp.route('/delete_recipe/<int:recipe_id>', methods=['POST'])
@login_required
def delete_recipe(recipe_id):
    recipe = Recipe.query.get_or_404(recipe_id)
    
    # Vérifier si la recette appartient à l'entreprise de l'utilisateur
    if recipe.business_id != current_user.owned_business[0].id:
        return jsonify({'success': False, 'message': 'Recette non autorisée'})
    
    db.session.delete(recipe)
    db.session.commit()
    
    return jsonify({'success': True, 'message': 'Recette supprimée avec succès'}) 