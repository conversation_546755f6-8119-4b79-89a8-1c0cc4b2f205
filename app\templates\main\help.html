{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <!-- Navigation de l'aide -->
        <div class="md:col-span-1">
            <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Sections</h3>
                </div>
                <div class="divide-y divide-gray-200 dark:divide-gray-700">
                    <a href="#getting-started" class="block px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">Premiers pas</a>
                    <a href="#sales" class="block px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">Ventes</a>
                    <a href="#inventory" class="block px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">Inventaire</a>
                    <a href="#reports" class="block px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">Rapports</a>
                    <a href="#settings" class="block px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">Paramètres</a>
                    <a href="#faq" class="block px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">FAQ</a>
                </div>
            </div>
        </div>

        <!-- Contenu de l'aide -->
        <div class="md:col-span-3">
            <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg overflow-hidden">
                <div class="p-6 space-y-8">
                    <section id="getting-started" class="space-y-4">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Premiers pas</h2>
                        <p class="text-gray-600 dark:text-gray-400">Bienvenue dans POS System ! Voici les étapes pour commencer :</p>
                        <ol class="list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-400">
                            <li>Créez votre compte en cliquant sur "Inscription"</li>
                            <li>Configurez votre entreprise et vos informations</li>
                            <li>Ajoutez vos produits et catégories</li>
                            <li>Commencez à effectuer des ventes</li>
                        </ol>
                    </section>

                    <section id="sales" class="space-y-4">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Ventes</h2>
                        <p class="text-gray-600 dark:text-gray-400">Le module de ventes vous permet de :</p>
                        <ul class="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-400">
                            <li>Effectuer des ventes rapides</li>
                            <li>Gérer les commandes en cours</li>
                            <li>Consulter l'historique des ventes</li>
                            <li>Imprimer des tickets</li>
                        </ul>
                    </section>

                    <section id="inventory" class="space-y-4">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Inventaire</h2>
                        <p class="text-gray-600 dark:text-gray-400">Gérez votre inventaire efficacement :</p>
                        <ul class="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-400">
                            <li>Ajoutez et modifiez des produits</li>
                            <li>Créez des catégories</li>
                            <li>Suivez les stocks</li>
                            <li>Gérez les ingrédients et les recettes</li>
                        </ul>
                    </section>

                    <section id="reports" class="space-y-4">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Rapports</h2>
                        <p class="text-gray-600 dark:text-gray-400">Analysez vos données avec nos rapports :</p>
                        <ul class="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-400">
                            <li>Rapports de ventes</li>
                            <li>Analyse d'inventaire</li>
                            <li>Rapports financiers</li>
                            <li>Statistiques détaillées</li>
                        </ul>
                    </section>

                    <section id="settings" class="space-y-4">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Paramètres</h2>
                        <p class="text-gray-600 dark:text-gray-400">Personnalisez votre système :</p>
                        <ul class="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-400">
                            <li>Gestion des utilisateurs</li>
                            <li>Configuration de l'entreprise</li>
                            <li>Paramètres d'impression</li>
                            <li>Configuration des tables</li>
                        </ul>
                    </section>

                    <section id="faq" class="space-y-4">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">FAQ</h2>
                        <div class="space-y-4">
                            <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Comment ajouter un nouveau produit ?</h3>
                                <p class="mt-2 text-gray-600 dark:text-gray-400">
                                    Allez dans la section "Inventaire" > "Produits" et cliquez sur le bouton "Ajouter un produit". Remplissez le formulaire avec les informations du produit et cliquez sur "Enregistrer".
                                </p>
                            </div>
                            <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Comment effectuer une vente ?</h3>
                                <p class="mt-2 text-gray-600 dark:text-gray-400">
                                    Dans la section "Ventes", sélectionnez les produits à vendre, ajustez les quantités si nécessaire, et cliquez sur "Terminer la vente". Choisissez ensuite le mode de paiement.
                                </p>
                            </div>
                            <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Comment gérer les stocks ?</h3>
                                <p class="mt-2 text-gray-600 dark:text-gray-400">
                                    La section "Inventaire" vous permet de suivre les stocks, de définir des seuils d'alerte et de recevoir des notifications lorsque les stocks sont bas.
                                </p>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 