{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Gestion des produits</h2>
        <a href="{{ url_for('inventory.add_product') }}"
           class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <i class="fas fa-plus mr-2"></i> Ajouter un produit
        </a>
    </div>

    <!-- Grille de produits -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {% for product in products %}
        <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg overflow-hidden flex flex-col">
            {% if product.image_path %}
                <img src="{{ product.image_path }}" alt="{{ product.name }}" class="h-48 w-full object-cover">
            {% else %}
                <div class="h-48 w-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                    <i class="fas fa-box fa-3x text-gray-400 dark:text-gray-500"></i>
                </div>
            {% endif %}
            <div class="p-6 flex-1 flex flex-col">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ product.name }}</h3>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-4 flex-1">{{ product.description }}</p>
                <div class="flex items-center justify-between mb-2">
                    <span class="text-xl font-bold text-gray-900 dark:text-white">{{ product.price|round(2) }} €</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if product.stock > 5 %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200{% elif product.stock > 0 %}bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200{% else %}bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200{% endif %}">
                        {{ product.stock }} {{ product.unit }}
                    </span>
                </div>
                <div class="flex space-x-2 mt-4">
                    <a href="{{ url_for('inventory.edit_product', product_id=product.id) }}"
                       class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                        <i class="fas fa-edit"></i>
                    </a>
                    <a href="{{ url_for('inventory.delete_product', product_id=product.id) }}"
                       class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                       onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce produit ?');">
                        <i class="fas fa-trash"></i>
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %} 