from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from . import main_bp
from app.models.models import Sale, Product, Category, SaleItem
from app import db
from datetime import datetime, timedelta
from sqlalchemy import select, func, desc
from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, PasswordField, SubmitField
from wtforms.validators import DataRequired, Email, EqualTo, Optional

class ProfileForm(FlaskForm):
    username = <PERSON><PERSON>ield('Nom d\'utilisateur', validators=[DataRequired()])
    email = StringField('Email', validators=[DataRequired(), Email()])
    current_password = PasswordField('Mot de passe actuel', validators=[Optional()])
    new_password = PasswordField('Nouveau mot de passe', validators=[Optional()])
    confirm_password = PasswordField('Confirmer le mot de passe', validators=[EqualTo('new_password')])
    submit = SubmitField('Mettre à jour')

@main_bp.app_context_processor
def utility_processor():
    def get_low_stock_count():
        if current_user.is_authenticated and current_user.owned_business:
            return Product.query.filter(
                Product.business_id == current_user.owned_business[0].id,
                Product.stock <= Product.min_stock
            ).count()
        return 0
    
    return dict(low_stock_count=get_low_stock_count())

@main_bp.route('/')
@main_bp.route('/index')
def index():
    if current_user.is_authenticated:
        # Récupérer les statistiques pour le tableau de bord
        today = datetime.utcnow().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())

        # Ventes du jour
        daily_sales = Sale.query.filter(
            Sale.business_id == current_user.owned_business[0].id,
            Sale.created_at >= today_start,
            Sale.created_at <= today_end
        ).all()

        daily_total = sum(sale.total_amount for sale in daily_sales)
        daily_count = len(daily_sales)

        # Produits en rupture de stock
        low_stock_products = Product.query.filter(
            Product.business_id == current_user.owned_business[0].id,
            Product.stock <= Product.min_stock
        ).all()

        # Catégories populaires
        query = select(Category, db.func.count(Sale.id).label('sale_count')).join(Product).join(SaleItem).join(Sale).group_by(Category.id).order_by(db.func.count(Sale.id).desc()).limit(5)
        result = db.session.execute(query).all()

        return render_template('main/index.html',
                             title='Tableau de bord',
                             daily_sales=daily_sales,
                             daily_total=daily_total,
                             daily_count=daily_count,
                             low_stock_products=low_stock_products,
                             popular_categories=result)
    return render_template('main/index.html', title='Accueil')

@main_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    form = ProfileForm()
    if form.validate_on_submit():
        current_user.username = form.username.data
        current_user.email = form.email.data
        
        if form.current_password.data:
            if not current_user.check_password(form.current_password.data):
                flash('Mot de passe actuel incorrect', 'error')
                return redirect(url_for('main.profile'))
            
            if form.new_password.data:
                current_user.set_password(form.new_password.data)
        
        db.session.commit()
        flash('Profil mis à jour avec succès', 'success')
        return redirect(url_for('main.profile'))
    
    elif request.method == 'GET':
        form.username.data = current_user.username
        form.email.data = current_user.email
    
    return render_template('main/profile.html', title='Profil', form=form)

@main_bp.route('/help')
def help():
    return render_template('main/help.html', title='Aide') 