{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- En-tête -->
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Historique des ventes</h2>
        <div class="flex space-x-3">
            <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200" onclick="exportSales()">
                <i class="fas fa-file-export mr-2"></i> Exporter
            </button>
        </div>
    </div>

    <!-- Filtres -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="startDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Date de début</label>
                <input type="date" id="startDate" class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
            </div>
            <div>
                <label for="endDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Date de fin</label>
                <input type="date" id="endDate" class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
            </div>
            <div>
                <label for="paymentMethod" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Méthode de paiement</label>
                <select id="paymentMethod" class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="">Toutes</option>
                    <option value="cash">Espèces</option>
                    <option value="card">Carte</option>
                    <option value="transfer">Virement</option>
                </select>
            </div>
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Recherche</label>
                <input type="text" id="search" placeholder="Rechercher..." class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
            </div>
        </div>
        <div class="mt-4 flex justify-end">
            <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200" onclick="applyFilters()">
                <i class="fas fa-filter mr-2"></i> Filtrer
            </button>
        </div>
    </div>

    <!-- Liste des ventes -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Table</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Articles</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Paiement</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Statut</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {% for sale in sales %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">#{{ sale.id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ sale.date }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">Table {{ sale.table_number }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ sale.items_count }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ sale.total }} €</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if sale.payment_method == 'cash' %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200{% elif sale.payment_method == 'card' %}bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200{% else %}bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200{% endif %}">
                                {% if sale.payment_method == 'cash' %}
                                    Espèces
                                {% elif sale.payment_method == 'card' %}
                                    Carte
                                {% else %}
                                    Virement
                                {% endif %}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if sale.status == 'completed' %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200{% else %}bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200{% endif %}">
                                {% if sale.status == 'completed' %}
                                    Terminée
                                {% else %}
                                    Annulée
                                {% endif %}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button type="button" class="text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 mr-3" data-sale-id="{{ sale.id }}" onclick="viewSaleDetails(this.dataset.saleId)">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="text-green-400 hover:text-green-500 dark:hover:text-green-300 mr-3" data-sale-id="{{ sale.id }}" onclick="printReceipt(this.dataset.saleId)">
                                <i class="fas fa-print"></i>
                            </button>
                            {% if sale.status == 'completed' %}
                            <button type="button" class="text-red-400 hover:text-red-500 dark:hover:text-red-300" data-sale-id="{{ sale.id }}" onclick="cancelSale(this.dataset.saleId)">
                                <i class="fas fa-times"></i>
                            </button>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <div class="mt-4 flex justify-between items-center">
        <div class="text-sm text-gray-700 dark:text-gray-300">
            Affichage de {{ pagination.start }} à {{ pagination.end }} sur {{ pagination.total }} ventes
        </div>
        <div class="flex space-x-2">
            {% if pagination.has_prev %}
            <button type="button" class="inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" data-page="{{ pagination.prev_num }}" onclick="changePage(this.dataset.page)">
                Précédent
            </button>
            {% endif %}
            {% if pagination.has_next %}
            <button type="button" class="inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" data-page="{{ pagination.next_num }}" onclick="changePage(this.dataset.page)">
                Suivant
            </button>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal des détails de vente -->
<div id="saleDetailsModal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
            <div>
                <div class="mt-3 text-center sm:mt-5">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                        Détails de la vente
                    </h3>
                    <div class="mt-4">
                        <div id="saleDetails" class="space-y-4">
                            <!-- Les détails seront chargés dynamiquement -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-5 sm:mt-6">
                <button type="button" class="w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm" onclick="closeSaleDetailsModal()">
                    Fermer
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Fonction pour appliquer les filtres
    function applyFilters() {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const paymentMethod = document.getElementById('paymentMethod').value;
        const search = document.getElementById('search').value;
        
        window.location.href = `/pos/history?start_date=${startDate}&end_date=${endDate}&payment_method=${paymentMethod}&search=${search}`;
    }

    // Fonction pour changer de page
    function changePage(page) {
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('page', page);
        window.location.href = `/pos/history?${urlParams.toString()}`;
    }

    // Fonction pour exporter les ventes
    function exportSales() {
        const urlParams = new URLSearchParams(window.location.search);
        window.location.href = `/pos/export_sales?${urlParams.toString()}`;
    }

    // Fonction pour afficher les détails d'une vente
    function viewSaleDetails(id) {
        fetch(`/pos/sale_details/${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const details = document.getElementById('saleDetails');
                    details.innerHTML = `
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Articles</h4>
                                <ul class="mt-2 space-y-2">
                                    ${data.sale.items.map(item => `
                                        <li class="flex justify-between text-sm">
                                            <span class="text-gray-900 dark:text-white">${item.name} x${item.quantity}</span>
                                            <span class="text-gray-900 dark:text-white">${item.total} €</span>
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                            <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500 dark:text-gray-400">Sous-total</span>
                                    <span class="text-gray-900 dark:text-white">${data.sale.subtotal} €</span>
                                </div>
                                <div class="flex justify-between text-sm mt-2">
                                    <span class="text-gray-500 dark:text-gray-400">TVA</span>
                                    <span class="text-gray-900 dark:text-white">${data.sale.tax} €</span>
                                </div>
                                <div class="flex justify-between text-base font-medium mt-4">
                                    <span class="text-gray-900 dark:text-white">Total</span>
                                    <span class="text-gray-900 dark:text-white">${data.sale.total} €</span>
                                </div>
                            </div>
                        </div>
                    `;
                    document.getElementById('saleDetailsModal').classList.remove('hidden');
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue');
            });
    }

    // Fonction pour fermer le modal des détails
    function closeSaleDetailsModal() {
        document.getElementById('saleDetailsModal').classList.add('hidden');
    }

    // Fonction pour imprimer un reçu
    function printReceipt(id) {
        window.open(`/pos/print_receipt/${id}`, '_blank');
    }

    // Fonction pour annuler une vente
    function cancelSale(id) {
        if (confirm('Êtes-vous sûr de vouloir annuler cette vente ?')) {
            fetch(`/pos/cancel_sale/${id}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue');
            });
        }
    }
</script>
{% endblock %} 