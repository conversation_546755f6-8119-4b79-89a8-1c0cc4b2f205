from flask import render_template, request, jsonify
from flask_login import login_required, current_user
from . import reports_bp
from app.models.models import Sale, SaleItem, Product, Category, StockMovement
from app import db
from datetime import datetime, timedelta
from sqlalchemy import func, desc

@reports_bp.route('/sales')
@reports_bp.route('/sales/')
@login_required
def sales_report():
    # Récupérer les paramètres de filtrage
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    category_id = request.args.get('category_id')
    
    # Construire la requête de base
    query = Sale.query.filter_by(business_id=current_user.owned_business[0].id)
    
    # Appliquer les filtres
    if start_date:
        query = query.filter(Sale.created_at >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        query = query.filter(Sale.created_at <= datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1))
    
    # Récupérer les ventes
    sales = query.order_by(Sale.created_at.desc()).all()
    
    # Calculer les statistiques
    total_sales = len(sales)
    total_amount = sum(sale.total_amount for sale in sales)
    average_basket = total_amount / total_sales if total_sales > 0 else 0
    
    # Récupérer les catégories pour le filtre
    categories = Category.query.filter_by(
        business_id=current_user.owned_business[0].id,
        type='product'
    ).all()
    
    return render_template('reports/sales.html',
                         title='Rapport des ventes',
                         sales=sales,
                         categories=categories,
                         total_sales=total_amount,
                         total_transactions=total_sales,
                         average_basket=average_basket,
                         start_date=start_date,
                         end_date=end_date,
                         category_id=category_id)

@reports_bp.route('/products')
@login_required
def products_report():
    # Récupérer les paramètres de filtrage
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    category_id = request.args.get('category_id')
    
    # Construire la requête de base
    query = SaleItem.query.join(Sale).filter(
        Sale.business_id == current_user.owned_business[0].id
    )
    
    # Appliquer les filtres
    if start_date:
        query = query.filter(Sale.created_at >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        query = query.filter(Sale.created_at <= datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1))
    if category_id:
        query = query.join(Product).filter(Product.category_id == category_id)
    
    # Récupérer les statistiques par produit
    products_stats = db.session.query(
        Product,
        func.sum(SaleItem.quantity).label('total_quantity'),
        func.sum(SaleItem.quantity * SaleItem.price).label('total_amount')
    ).join(SaleItem).join(Sale).filter(
        Sale.business_id == current_user.owned_business[0].id
    ).group_by(Product).order_by(desc('total_amount')).all()
    
    # Récupérer les catégories pour le filtre
    categories = Category.query.filter_by(
        business_id=current_user.owned_business[0].id,
        type='product'
    ).all()
    
    return render_template('reports/products.html',
                         title='Rapport des produits',
                         products_stats=products_stats,
                         categories=categories)

@reports_bp.route('/stock')
@login_required
def stock_report():
    # Récupérer les paramètres de filtrage
    category_id = request.args.get('category_id')
    stock_status = request.args.get('stock_status')
    
    # Construire la requête de base
    query = Product.query.filter_by(business_id=current_user.owned_business[0].id)
    
    # Appliquer les filtres
    if category_id:
        query = query.filter_by(category_id=category_id)
    if stock_status == 'low':
        query = query.filter(Product.stock <= Product.min_stock)
    elif stock_status == 'out':
        query = query.filter(Product.stock == 0)
    
    # Récupérer les produits
    products = query.order_by(Product.name).all()
    
    # Récupérer les catégories pour le filtre
    categories = Category.query.filter_by(
        business_id=current_user.owned_business[0].id,
        type='product'
    ).all()
    
    return render_template('reports/stock.html',
                         title='Rapport des stocks',
                         products=products,
                         categories=categories)

@reports_bp.route('/export/<report_type>')
@login_required
def export_report(report_type):
    # Récupérer les paramètres de filtrage
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    category_id = request.args.get('category_id')
    
    # Générer le rapport selon le type
    if report_type == 'sales':
        # Logique d'export des ventes
        pass
    elif report_type == 'products':
        # Logique d'export des produits
        pass
    elif report_type == 'stock':
        # Logique d'export des stocks
        pass
    
    return jsonify({'success': True, 'message': 'Rapport exporté avec succès'}) 