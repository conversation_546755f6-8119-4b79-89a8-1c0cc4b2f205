{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
{% if current_user.is_authenticated %}
    <!-- Modern Dashboard Header -->
    <div class="px-6 py-8">
        <div class="flex items-center justify-between mb-8 animate-fade-in-up">
            <div>
                <h1 class="text-3xl font-bold text-neutral-100 mb-2">
                    Tableau de bord
                    <span class="text-primary-400">FuturePOS</span>
                </h1>
                <p class="text-neutral-400">
                    Bienvenue, <span class="text-secondary-400 font-medium">{{ current_user.username }}</span> •
                    <span id="currentDate"></span>
                </p>
            </div>
            <div class="flex items-center gap-4">
                <div class="glass-card px-4 py-2">
                    <div class="flex items-center gap-2 text-sm">
                        <div class="w-2 h-2 bg-accent-400 rounded-full animate-pulse"></div>
                        <span class="text-neutral-300">Système en ligne</span>
                    </div>
                </div>
                <a href="{{ url_for('pos.new_sale') }}" class="btn-primary-modern">
                    <i data-lucide="plus" class="w-4 h-4"></i>
                    Nouvelle vente
                </a>
            </div>
        </div>

        <!-- Modern Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 stagger-children">
            <!-- Daily Sales -->
            <div class="glass-card p-6 hover-lift animate-on-scroll">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center">
                        <i data-lucide="trending-up" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold font-mono text-accent-400">{{ daily_total|round(2) }}€</div>
                        <div class="text-xs text-neutral-400">+12% vs hier</div>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-neutral-300">Ventes du jour</h3>
                        <p class="text-xs text-neutral-500">{{ daily_count }} transactions</p>
                    </div>
                    <div class="w-16 h-8 bg-gradient-to-r from-accent-500/20 to-accent-400/20 rounded-md flex items-end justify-center">
                        <div class="w-1 h-2 bg-accent-400 rounded-sm mr-1"></div>
                        <div class="w-1 h-4 bg-accent-400 rounded-sm mr-1"></div>
                        <div class="w-1 h-6 bg-accent-400 rounded-sm mr-1"></div>
                        <div class="w-1 h-3 bg-accent-400 rounded-sm"></div>
                    </div>
                </div>
            </div>

            <!-- Orders Count -->
            <div class="glass-card p-6 hover-lift animate-on-scroll">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
                        <i data-lucide="shopping-bag" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold font-mono text-primary-400">{{ daily_count }}</div>
                        <div class="text-xs text-neutral-400">+8% vs hier</div>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-neutral-300">Commandes</h3>
                        <p class="text-xs text-neutral-500">Aujourd'hui</p>
                    </div>
                    <div class="w-16 h-8 bg-gradient-to-r from-primary-500/20 to-primary-400/20 rounded-md flex items-end justify-center">
                        <div class="w-1 h-3 bg-primary-400 rounded-sm mr-1"></div>
                        <div class="w-1 h-5 bg-primary-400 rounded-sm mr-1"></div>
                        <div class="w-1 h-4 bg-primary-400 rounded-sm mr-1"></div>
                        <div class="w-1 h-6 bg-primary-400 rounded-sm"></div>
                    </div>
                </div>
            </div>

            <!-- Average Order -->
            <div class="glass-card p-6 hover-lift animate-on-scroll">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-secondary-500 to-secondary-600 flex items-center justify-center">
                        <i data-lucide="calculator" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold font-mono text-secondary-400">
                            {% if daily_count > 0 %}{{ (daily_total / daily_count)|round(2) }}€{% else %}0€{% endif %}
                        </div>
                        <div class="text-xs text-neutral-400">Panier moyen</div>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-neutral-300">Ticket moyen</h3>
                        <p class="text-xs text-neutral-500">Par commande</p>
                    </div>
                    <div class="w-16 h-8 bg-gradient-to-r from-secondary-500/20 to-secondary-400/20 rounded-md flex items-end justify-center">
                        <div class="w-1 h-4 bg-secondary-400 rounded-sm mr-1"></div>
                        <div class="w-1 h-2 bg-secondary-400 rounded-sm mr-1"></div>
                        <div class="w-1 h-5 bg-secondary-400 rounded-sm mr-1"></div>
                        <div class="w-1 h-3 bg-secondary-400 rounded-sm"></div>
                    </div>
                </div>
            </div>

            <!-- Low Stock Alert -->
            <div class="glass-card p-6 hover-lift animate-on-scroll">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center">
                        <i data-lucide="alert-triangle" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold font-mono text-red-400">{{ low_stock_products|length }}</div>
                        <div class="text-xs text-neutral-400">Produits</div>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-neutral-300">Stock faible</h3>
                        <p class="text-xs text-neutral-500">Nécessite attention</p>
                    </div>
                    {% if low_stock_products|length > 0 %}
                    <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Modern Data Tables Section -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Recent Sales -->
            <div class="lg:col-span-2 glass-card p-6 animate-on-scroll">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-neutral-100 flex items-center gap-2">
                        <i data-lucide="activity" class="w-5 h-5 text-primary-400"></i>
                        Dernières ventes
                    </h3>
                    <a href="{{ url_for('pos.sales_history') }}" class="btn-ghost-modern text-sm">
                        Voir tout
                        <i data-lucide="arrow-right" class="w-3 h-3"></i>
                    </a>
                </div>

                {% if daily_sales %}
                    <div class="space-y-3">
                        {% for sale in daily_sales[:5] %}
                        <div class="flex items-center justify-between p-4 rounded-lg bg-neutral-800/50 border border-neutral-700 hover:border-primary-500/30 transition-colors">
                            <div class="flex items-center gap-4">
                                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
                                    <span class="text-white font-bold text-sm">#{{ sale.id }}</span>
                                </div>
                                <div>
                                    <div class="font-medium text-neutral-100">{{ sale.created_at.strftime('%H:%M') }}</div>
                                    <div class="text-sm text-neutral-400">
                                        {% if sale.table_number %}Table {{ sale.table_number }}{% else %}À emporter{% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="font-bold font-mono text-accent-400">{{ sale.total_amount|round(2) }}€</div>
                                <div class="flex items-center gap-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {% if sale.status == 'paid' %}bg-accent-500/20 text-accent-400{% else %}bg-yellow-500/20 text-yellow-400{% endif %}">
                                        {{ sale.status }}
                                    </span>
                                    <span class="text-xs text-neutral-500">{{ sale.payment_method }}</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-12 text-neutral-400">
                        <i data-lucide="receipt" class="w-12 h-12 mx-auto mb-4 opacity-50"></i>
                        <p class="text-sm">Aucune vente aujourd'hui</p>
                        <p class="text-xs mt-1">Les ventes apparaîtront ici</p>
                    </div>
                {% endif %}
            </div>

            <!-- Stock Alerts & Quick Actions -->
            <div class="space-y-6">
                <!-- Low Stock Products -->
                <div class="glass-card p-6 animate-on-scroll">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold text-neutral-100 flex items-center gap-2">
                            <i data-lucide="package-x" class="w-4 h-4 text-red-400"></i>
                            Stock faible
                        </h3>
                        {% if low_stock_products|length > 0 %}
                        <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                        {% endif %}
                    </div>

                    {% if low_stock_products %}
                        <div class="space-y-3">
                            {% for product in low_stock_products[:3] %}
                            <div class="flex items-center justify-between p-3 rounded-lg bg-red-500/10 border border-red-500/20">
                                <div>
                                    <div class="font-medium text-neutral-100 text-sm">{{ product.name }}</div>
                                    <div class="text-xs text-neutral-400">{{ product.category.name }}</div>
                                </div>
                                <div class="text-right">
                                    <div class="font-mono text-red-400 text-sm">{{ product.stock }} {{ product.unit }}</div>
                                    <div class="text-xs text-neutral-500">Min: {{ product.min_stock }}</div>
                                </div>
                            </div>
                            {% endfor %}
                            {% if low_stock_products|length > 3 %}
                            <div class="text-center">
                                <span class="text-xs text-neutral-400">+{{ low_stock_products|length - 3 }} autres produits</span>
                            </div>
                            {% endif %}
                        </div>
                    {% else %}
                        <div class="text-center py-8 text-neutral-400">
                            <i data-lucide="check-circle" class="w-8 h-8 mx-auto mb-2 text-accent-400"></i>
                            <p class="text-sm">Stock optimal</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Quick Actions -->
                <div class="glass-card p-6 animate-on-scroll">
                    <h3 class="text-lg font-bold text-neutral-100 mb-4 flex items-center gap-2">
                        <i data-lucide="zap" class="w-4 h-4 text-secondary-400"></i>
                        Actions rapides
                    </h3>
                    <div class="space-y-3">
                        <a href="{{ url_for('pos.new_sale') }}" class="btn-primary-modern w-full justify-center">
                            <i data-lucide="plus-circle" class="w-4 h-4"></i>
                            Nouvelle vente
                        </a>
                        <a href="{{ url_for('pos.pending_sales') }}" class="btn-ghost-modern w-full justify-center">
                            <i data-lucide="clock" class="w-4 h-4"></i>
                            Commandes en cours
                        </a>
                        <a href="{{ url_for('pos.sales_history') }}" class="btn-ghost-modern w-full justify-center">
                            <i data-lucide="history" class="w-4 h-4"></i>
                            Historique
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% else %}
    <!-- Modern Landing Page -->
    <div class="min-h-screen flex items-center justify-center px-6">
        <div class="max-w-4xl mx-auto text-center animate-fade-in-up">
            <!-- Hero Section -->
            <div class="mb-12">
                <div class="flex justify-center mb-8">
                    <div class="relative">
                        <div class="w-24 h-24 rounded-2xl bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center shadow-2xl animate-float">
                            <i data-lucide="store" class="w-12 h-12 text-white"></i>
                        </div>
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-accent-400 rounded-full animate-pulse"></div>
                    </div>
                </div>

                <h1 class="text-5xl md:text-7xl font-bold text-neutral-100 mb-6">
                    Bienvenue sur
                    <span class="bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                        FuturePOS
                    </span>
                </h1>

                <p class="text-xl md:text-2xl text-neutral-300 mb-8 max-w-2xl mx-auto">
                    Le système de point de vente le plus moderne et futuriste pour restaurants
                </p>

                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ url_for('auth.register') }}" class="btn-primary-modern text-lg px-8 py-4">
                        <i data-lucide="rocket" class="w-5 h-5"></i>
                        Commencer maintenant
                    </a>
                    <a href="{{ url_for('auth.login') }}" class="btn-ghost-modern text-lg px-8 py-4">
                        <i data-lucide="log-in" class="w-5 h-5"></i>
                        Se connecter
                    </a>
                </div>
            </div>

            <!-- Features Grid -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 stagger-children">
                <div class="glass-card p-6 hover-lift">
                    <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="touch" class="w-6 h-6 text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold text-neutral-100 mb-2">Interface tactile</h3>
                    <p class="text-neutral-400 text-sm">Interface moderne et intuitive optimisée pour les écrans tactiles</p>
                </div>

                <div class="glass-card p-6 hover-lift">
                    <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-secondary-500 to-secondary-600 flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="trending-up" class="w-6 h-6 text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold text-neutral-100 mb-2">Analytics temps réel</h3>
                    <p class="text-neutral-400 text-sm">Suivez vos ventes et performances en temps réel</p>
                </div>

                <div class="glass-card p-6 hover-lift">
                    <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="cloud" class="w-6 h-6 text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold text-neutral-100 mb-2">Cloud SaaS</h3>
                    <p class="text-neutral-400 text-sm">Accédez à vos données depuis n'importe où, à tout moment</p>
                </div>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
// Display current date
document.addEventListener('DOMContentLoaded', () => {
    const dateElement = document.getElementById('currentDate');
    if (dateElement) {
        const now = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        dateElement.textContent = now.toLocaleDateString('fr-FR', options);
    }

    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>
{% endblock %}