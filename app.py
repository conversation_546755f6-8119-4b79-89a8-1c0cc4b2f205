from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_migrate import Migrate
from flask_mail import Mail
from flask_bcrypt import Bcrypt
from flask_caching import Cache
from flask_session import Session
from flask_principal import Principal
from flask_babel import Babel
import os
from dotenv import load_dotenv

# Chargement des variables d'environnement
load_dotenv()

# Initialisation de l'application
app = Flask(__name__)

# Configuration de base
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-key-change-in-production')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'postgresql://localhost/pos_system')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['MAIL_SERVER'] = os.getenv('MAIL_SERVER', 'smtp.gmail.com')
app.config['MAIL_PORT'] = int(os.getenv('MAIL_PORT', 587))
app.config['MAIL_USE_TLS'] = os.getenv('MAIL_USE_TLS', 'True').lower() == 'true'
app.config['MAIL_USERNAME'] = os.getenv('MAIL_USERNAME')
app.config['MAIL_PASSWORD'] = os.getenv('MAIL_PASSWORD')
app.config['REDIS_URL'] = os.getenv('REDIS_URL', 'redis://localhost:6379/0')

# Configuration de la session
app.config['SESSION_TYPE'] = 'redis'
app.config['SESSION_REDIS'] = app.config['REDIS_URL']

# Initialisation des extensions
db = SQLAlchemy(app)
migrate = Migrate(app, db)
login_manager = LoginManager(app)
mail = Mail(app)
bcrypt = Bcrypt(app)
cache = Cache(app)
Session(app)
principal = Principal(app)
babel = Babel(app)

# Configuration du login manager
login_manager.login_view = 'auth.login'
login_manager.login_message_category = 'info'

# Import des blueprints
from auth import auth_bp
from main import main_bp
from admin import admin_bp
from pos import pos_bp
from inventory import inventory_bp
from reports import reports_bp
from settings import settings_bp

# Enregistrement des blueprints
app.register_blueprint(auth_bp)
app.register_blueprint(main_bp)
app.register_blueprint(admin_bp)
app.register_blueprint(pos_bp)
app.register_blueprint(inventory_bp)
app.register_blueprint(reports_bp)
app.register_blueprint(settings_bp)

if __name__ == '__main__':
    app.run(debug=True) 