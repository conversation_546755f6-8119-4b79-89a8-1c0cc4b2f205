{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title mb-0">Ajouter un ingrédient</h2>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="name" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="category_id" class="form-label">Catégorie</label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Sélectionner une catégorie</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Prix</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="price" name="price" 
                                               step="0.01" min="0" required>
                                        <span class="input-group-text">€</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="stock" class="form-label">Stock initial</label>
                                    <input type="number" class="form-control" id="stock" name="stock" 
                                           step="0.01" min="0" value="0">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="unit" class="form-label">Unité</label>
                                    <select class="form-select" id="unit" name="unit" required>
                                        <option value="kg">Kilogramme (kg)</option>
                                        <option value="g">Gramme (g)</option>
                                        <option value="l">Litre (l)</option>
                                        <option value="ml">Millilitre (ml)</option>
                                        <option value="unit">Unité</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="min_stock" class="form-label">Stock minimum</label>
                                    <input type="number" class="form-control" id="min_stock" name="min_stock" 
                                           step="0.01" min="0" value="0">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="expiry_date" class="form-label">Date d'expiration</label>
                            <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('inventory.ingredients') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 