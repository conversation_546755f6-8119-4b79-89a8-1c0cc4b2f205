// Fonction pour afficher les notifications
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    } text-white`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Fonction pour gérer le numpad
function handleNumpad() {
    const numpad = document.querySelector('.numpad');
    if (!numpad) return;

    let currentValue = '';
    const display = document.querySelector('.numpad-display');

    numpad.addEventListener('click', (e) => {
        const button = e.target;
        if (!button.classList.contains('numpad-btn')) return;

        const value = button.dataset.value;
        
        if (value === 'clear') {
            currentValue = '';
        } else if (value === 'backspace') {
            currentValue = currentValue.slice(0, -1);
        } else if (value === 'enter') {
            if (currentValue) {
                // Ajouter la quantité au produit sélectionné
                const selectedProduct = document.querySelector('.product-card.selected');
                if (selectedProduct) {
                    const productId = selectedProduct.dataset.productId;
                    addToCart(productId, parseFloat(currentValue));
                }
                currentValue = '';
            }
        } else {
            currentValue += value;
        }

        if (display) {
            display.textContent = currentValue;
        }
    });
}

// Fonction pour ajouter un produit au panier
function addToCart(productId, quantity = 1) {
    fetch('/pos/add_to_cart', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCart(data.cart);
            showNotification('Produit ajouté au panier');
        } else {
            showNotification(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Erreur lors de l\'ajout au panier', 'danger');
    });
}

// Fonction pour mettre à jour l'affichage du panier
function updateCart(cartData) {
    const cartContainer = document.querySelector('.cart-items');
    if (!cartContainer) return;

    cartContainer.innerHTML = '';
    let total = 0;

    cartData.items.forEach(item => {
        const itemElement = document.createElement('div');
        itemElement.className = 'cart-item';
        itemElement.innerHTML = `
            <div class="cart-item-details">
                <span class="cart-item-name">${item.name}</span>
                <span class="cart-item-quantity">x${item.quantity}</span>
            </div>
            <div class="cart-item-price">${item.total.toFixed(2)} €</div>
        `;
        cartContainer.appendChild(itemElement);
        total += item.total;
    });

    const totalElement = document.querySelector('.cart-total');
    if (totalElement) {
        totalElement.textContent = `Total: ${total.toFixed(2)} €`;
    }
}

// Fonction pour gérer la recherche de produits
function handleProductSearch() {
    const searchInput = document.querySelector('.product-search');
    if (!searchInput) return;

    searchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        const products = document.querySelectorAll('.product-card');

        products.forEach(product => {
            const productName = product.querySelector('.product-name').textContent.toLowerCase();
            if (productName.includes(searchTerm)) {
                product.style.display = '';
            } else {
                product.style.display = 'none';
            }
        });
    });
}

// Fonction pour gérer le filtrage par catégorie
function handleCategoryFilter() {
    const categoryButtons = document.querySelectorAll('.category-btn');
    if (!categoryButtons.length) return;

    categoryButtons.forEach(button => {
        button.addEventListener('click', () => {
            const category = button.dataset.category;
            const products = document.querySelectorAll('.product-card');

            products.forEach(product => {
                if (category === 'all' || product.dataset.category === category) {
                    product.style.display = '';
                } else {
                    product.style.display = 'none';
                }
            });

            // Mettre à jour l'état actif du bouton
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
        });
    });
}

// Fonction pour gérer la pagination des produits
function handleProductPagination() {
    const productsPerPage = 20;
    const products = document.querySelectorAll('.product-card');
    const totalPages = Math.ceil(products.length / productsPerPage);
    let currentPage = 1;

    function showPage(page) {
        const start = (page - 1) * productsPerPage;
        const end = start + productsPerPage;

        products.forEach((product, index) => {
            if (index >= start && index < end) {
                product.style.display = '';
            } else {
                product.style.display = 'none';
            }
        });
    }

    // Créer les boutons de pagination
    const pagination = document.querySelector('.pagination');
    if (pagination) {
        for (let i = 1; i <= totalPages; i++) {
            const button = document.createElement('button');
            button.className = 'btn btn-outline-primary mx-1';
            button.textContent = i;
            button.addEventListener('click', () => {
                currentPage = i;
                showPage(currentPage);
            });
            pagination.appendChild(button);
        }
    }

    // Afficher la première page
    showPage(1);
}

// Initialisation des fonctions au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    handleNumpad();
    handleProductSearch();
    handleCategoryFilter();
    handleProductPagination();
}); 