from flask import render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from urllib.parse import urlparse
from . import auth_bp
from app.models.models import User, Business
from app import db
from .forms import LoginForm, RegistrationForm, ResetPasswordRequestForm, ResetPasswordForm
from app.auth.email import send_password_reset_email

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        if user is None or not user.check_password(form.password.data):
            flash('Email ou mot de passe invalide')
            return redirect(url_for('auth.login'))
        login_user(user, remember=form.remember_me.data)
        next_page = request.args.get('next')
        if not next_page or urlparse(next_page).netloc != '':
            next_page = url_for('main.index')
        return redirect(next_page)
    return render_template('auth/login.html', title='Connexion', form=form)

@auth_bp.route('/logout')
def logout():
    logout_user()
    return redirect(url_for('main.index'))

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(username=form.username.data, email=form.email.data, role='owner')
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()
        
        # Créer le business pour le nouvel owner
        business = Business(name=form.project_name.data, type=form.project_type.data, owner_id=user.id)
        db.session.add(business)
        db.session.commit()
        
        flash('Félicitations, vous êtes maintenant inscrit!')
        return redirect(url_for('auth.login'))
    return render_template('auth/register.html', title='Inscription', form=form)

@auth_bp.route('/reset_password_request', methods=['GET', 'POST'])
def reset_password_request():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    form = ResetPasswordRequestForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        if user:
            # Envoyer l'email de réinitialisation
            pass
        flash('Vérifiez votre email pour les instructions de réinitialisation du mot de passe')
        return redirect(url_for('auth.login'))
    return render_template('auth/reset_password_request.html', title='Réinitialisation du mot de passe', form=form)

@auth_bp.route('/reset_password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    form = ResetPasswordForm()
    if form.validate_on_submit():
        # Vérifier le token et réinitialiser le mot de passe
        pass
    return render_template('auth/reset_password.html', form=form) 