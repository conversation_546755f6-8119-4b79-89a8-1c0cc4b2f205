<!DOCTYPE html>
<html lang="fr" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>{% block title %}{% endblock %} - FuturePOS</title>

    <!-- Preload Critical Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Modern Icons -->
    <link href="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Font Awesome for additional icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/animations.css') }}" rel="stylesheet">

    <!-- Tailwind CSS for utility classes -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'mono': ['JetBrains Mono', 'monospace'],
                    },
                    colors: {
                        primary: {
                            50: '#f0f4ff',
                            100: '#e0edff',
                            200: '#c7ddff',
                            300: '#a4c7ff',
                            400: '#7ca6ff',
                            500: '#5b82ff',
                            600: '#4c63d2',
                            700: '#3d4ba6',
                            800: '#2e3a7a',
                            900: '#1f2951',
                        },
                        secondary: {
                            50: '#f0fdff',
                            100: '#ccfbff',
                            200: '#99f6ff',
                            300: '#66f0ff',
                            400: '#33e9ff',
                            500: '#00e1ff',
                            600: '#00b8d4',
                            700: '#008fa3',
                            800: '#006672',
                            900: '#003d41',
                        },
                        accent: {
                            50: '#f0fff4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    },
                    backdropBlur: {
                        xs: '2px',
                    }
                }
            }
        }
    </script>

    {% block styles %}{% endblock %}
</head>
<body class="h-full overflow-x-hidden">
    <!-- Background Effects -->
    <div class="fixed inset-0 z-0">
        <div class="absolute inset-0 bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900"></div>
        <div class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(91,130,255,0.1),transparent_50%)]"></div>
        <div class="absolute top-0 left-1/4 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-0 right-1/4 w-96 h-96 bg-secondary-500/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
    </div>

    <!-- Navigation -->
    {% include 'navbar.html' %}

    <!-- Main Content -->
    <main class="relative z-10 min-h-screen">
        <!-- Flash Messages -->
        <div id="flash-messages" class="fixed top-20 right-4 z-50 space-y-2">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="flash-message animate-slide-in-right glass-card p-4 max-w-sm {% if category == 'success' %}border-l-4 border-accent-500{% elif category == 'error' %}border-l-4 border-red-500{% elif category == 'warning' %}border-l-4 border-yellow-500{% else %}border-l-4 border-primary-500{% endif %}">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    {% if category == 'success' %}
                                        <i class="fas fa-check-circle text-accent-400"></i>
                                    {% elif category == 'error' %}
                                        <i class="fas fa-exclamation-circle text-red-400"></i>
                                    {% elif category == 'warning' %}
                                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                    {% else %}
                                        <i class="fas fa-info-circle text-primary-400"></i>
                                    {% endif %}
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-neutral-100">{{ message }}</p>
                                </div>
                                <div class="ml-auto pl-3">
                                    <button class="flash-close text-neutral-400 hover:text-neutral-100 transition-colors">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>

        <!-- Page Content -->
        <div class="animate-fade-in-up">
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 z-50 bg-neutral-900/80 backdrop-blur-sm hidden">
        <div class="flex items-center justify-center h-full">
            <div class="glass-card p-8 text-center">
                <div class="loading-spinner mx-auto mb-4"></div>
                <p class="text-neutral-100 font-medium">Chargement...</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- Initialize Lucide Icons -->
    <script>
        lucide.createIcons();
    </script>

    <!-- Modern JavaScript Enhancements -->
    <script>
        // Modern theme and interaction enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize dark mode
            document.documentElement.classList.add('dark');

            // Auto-hide flash messages
            setTimeout(() => {
                const flashMessages = document.querySelectorAll('.flash-message');
                flashMessages.forEach(msg => {
                    msg.style.animation = 'slideInFromRight 0.3s ease-out reverse';
                    setTimeout(() => msg.remove(), 300);
                });
            }, 5000);

            // Flash message close buttons
            document.querySelectorAll('.flash-close').forEach(btn => {
                btn.addEventListener('click', function() {
                    const message = this.closest('.flash-message');
                    message.style.animation = 'slideInFromRight 0.3s ease-out reverse';
                    setTimeout(() => message.remove(), 300);
                });
            });

            // Add smooth scrolling
            document.documentElement.style.scrollBehavior = 'smooth';

            // Initialize stagger animations for elements
            const staggerElements = document.querySelectorAll('.stagger-children');
            staggerElements.forEach(container => {
                const children = container.children;
                Array.from(children).forEach((child, index) => {
                    child.style.animationDelay = `${index * 0.1}s`;
                });
            });

            // Add intersection observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in-up');
                    }
                });
            }, observerOptions);

            // Observe elements with animation classes
            document.querySelectorAll('.animate-on-scroll').forEach(el => {
                observer.observe(el);
            });
        });

        // Global loading functions
        window.showLoading = function() {
            document.getElementById('loading-overlay').classList.remove('hidden');
        };

        window.hideLoading = function() {
            document.getElementById('loading-overlay').classList.add('hidden');
        };

        // Global notification function
        window.showNotification = function(message, type = 'info', duration = 5000) {
            const container = document.getElementById('flash-messages');
            const notification = document.createElement('div');

            const iconMap = {
                success: 'fa-check-circle text-accent-400',
                error: 'fa-exclamation-circle text-red-400',
                warning: 'fa-exclamation-triangle text-yellow-400',
                info: 'fa-info-circle text-primary-400'
            };

            const borderMap = {
                success: 'border-accent-500',
                error: 'border-red-500',
                warning: 'border-yellow-500',
                info: 'border-primary-500'
            };

            notification.className = `flash-message animate-slide-in-right glass-card p-4 max-w-sm border-l-4 ${borderMap[type]}`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas ${iconMap[type]}"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-neutral-100">${message}</p>
                    </div>
                    <div class="ml-auto pl-3">
                        <button class="flash-close text-neutral-400 hover:text-neutral-100 transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;

            container.appendChild(notification);

            // Add close functionality
            notification.querySelector('.flash-close').addEventListener('click', function() {
                notification.style.animation = 'slideInFromRight 0.3s ease-out reverse';
                setTimeout(() => notification.remove(), 300);
            });

            // Auto remove
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideInFromRight 0.3s ease-out reverse';
                    setTimeout(() => notification.remove(), 300);
                }
            }, duration);
        };
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>