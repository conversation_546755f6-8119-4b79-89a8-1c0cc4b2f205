{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block content %}
<!-- Modern POS Interface -->
<div class="pos-container-modern animate-fade-in-up">
    <!-- Left Panel - Categories & Quick Actions -->
    <div class="glass-card p-6 animate-slide-in-left">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-neutral-100 flex items-center gap-2">
                <i data-lucide="layers" class="w-5 h-5 text-primary-400"></i>
                Catégories
            </h2>
            <button class="quick-action-btn-modern" onclick="showQuickActions()">
                <i data-lucide="zap" class="w-4 h-4"></i>
            </button>
        </div>

        <!-- Categories -->
        <div class="space-y-2 mb-6">
            <button type="button" class="category-btn-modern active w-full" data-category="all">
                <i data-lucide="grid-3x3" class="w-4 h-4"></i>
                <span>Tous les produits</span>
                <span class="ml-auto text-xs bg-primary-500/20 text-primary-300 px-2 py-1 rounded-full">{{ products|length }}</span>
            </button>
            {% for category in categories %}
            <button type="button" class="category-btn-modern w-full" data-category="{{ category.id }}">
                <i data-lucide="tag" class="w-4 h-4"></i>
                <span>{{ category.name }}</span>
                <span class="ml-auto text-xs bg-neutral-700 text-neutral-300 px-2 py-1 rounded-full">{{ category.products|length if category.products else 0 }}</span>
            </button>
            {% endfor %}
        </div>

        <!-- Quantity Input -->
        <div class="glass-card p-4 border border-primary-500/30">
            <div class="text-center mb-4">
                <label class="text-sm font-medium text-neutral-300 mb-2 block">Quantité</label>
                <div class="relative">
                    <input type="number"
                           id="quantityInput"
                           class="input-modern text-center text-2xl font-bold font-mono h-16"
                           value="1"
                           min="0.1"
                           max="999"
                           step="0.1">
                    <div class="absolute inset-0 pointer-events-none border-2 border-transparent rounded-lg transition-colors duration-300" id="quantityBorder"></div>
                </div>
            </div>

            <!-- Quick Quantity Buttons -->
            <div class="grid grid-cols-3 gap-2">
                <button class="btn-ghost-modern text-xs py-2" onclick="setQuantity(0.5)">0.5</button>
                <button class="btn-ghost-modern text-xs py-2" onclick="setQuantity(1)">1</button>
                <button class="btn-ghost-modern text-xs py-2" onclick="setQuantity(2)">2</button>
                <button class="btn-ghost-modern text-xs py-2" onclick="setQuantity(5)">5</button>
                <button class="btn-ghost-modern text-xs py-2" onclick="setQuantity(10)">10</button>
                <button class="btn-ghost-modern text-xs py-2" onclick="clearQuantity()">
                    <i data-lucide="x" class="w-3 h-3"></i>
                </button>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="mt-6 space-y-3">
            <div class="flex items-center justify-between text-sm">
                <span class="text-neutral-400">Produits disponibles</span>
                <span class="text-accent-400 font-mono">{{ products|length }}</span>
            </div>
            <div class="flex items-center justify-between text-sm">
                <span class="text-neutral-400">Catégories</span>
                <span class="text-secondary-400 font-mono">{{ categories|length }}</span>
            </div>
        </div>
    </div>

    <!-- Center Panel - Products Grid -->
    <div class="flex flex-col animate-fade-in-up" style="animation-delay: 0.1s;">
        <!-- Search & Filters Header -->
        <div class="glass-card p-4 mb-6">
            <div class="flex items-center gap-4">
                <!-- Search Input -->
                <div class="flex-1 relative">
                    <input type="text"
                           id="searchInput"
                           class="input-modern pl-12 pr-4"
                           placeholder="Rechercher un produit...">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i data-lucide="search" class="w-4 h-4 text-neutral-400"></i>
                    </div>
                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center">
                        <kbd class="px-2 py-1 text-xs font-semibold text-neutral-400 bg-neutral-800 border border-neutral-600 rounded">Ctrl+F</kbd>
                    </div>
                </div>

                <!-- View Toggle -->
                <div class="flex items-center bg-neutral-800 rounded-lg p-1">
                    <button class="view-toggle-btn active" data-view="grid">
                        <i data-lucide="grid-3x3" class="w-4 h-4"></i>
                    </button>
                    <button class="view-toggle-btn" data-view="list">
                        <i data-lucide="list" class="w-4 h-4"></i>
                    </button>
                </div>

                <!-- Sort Options -->
                <select class="input-modern w-auto">
                    <option value="name">Nom A-Z</option>
                    <option value="price-asc">Prix croissant</option>
                    <option value="price-desc">Prix décroissant</option>
                    <option value="stock">Stock disponible</option>
                </select>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="products-grid-modern stagger-children" id="productsGrid">
            {% for product in products %}
            <div class="product-card-modern animate-on-scroll"
                 data-product-id="{{ product.id }}"
                 data-category="{{ product.category.id }}"
                 data-name="{{ product.name|lower }}"
                 data-price="{{ product.price }}">

                <!-- Product Image/Icon -->
                <div class="product-image-modern">
                    {% if product.image_path %}
                        <img src="{{ product.image_path }}" alt="{{ product.name }}" class="w-full h-full object-cover">
                    {% else %}
                        <i data-lucide="package" class="w-8 h-8 text-neutral-400"></i>
                    {% endif %}
                </div>

                <!-- Product Info -->
                <div class="flex-1 flex flex-col justify-between">
                    <div>
                        <h3 class="product-name-modern">{{ product.name }}</h3>
                        <p class="text-xs text-neutral-400 mb-2">{{ product.category.name }}</p>
                    </div>

                    <div class="space-y-2">
                        <!-- Price -->
                        <div class="product-price-modern">{{ product.price|round(2) }} €</div>

                        <!-- Stock Info -->
                        <div class="flex items-center justify-between text-xs">
                            <span class="text-neutral-400">Stock:</span>
                            <span class="font-mono {% if product.stock <= product.min_stock %}text-red-400{% else %}text-accent-400{% endif %}">
                                {{ product.stock }} {{ product.unit }}
                            </span>
                        </div>

                        <!-- Quick Add Button -->
                        <button class="btn-ghost-modern w-full text-xs py-1 opacity-0 group-hover:opacity-100 transition-opacity"
                                onclick="quickAdd({{ product.id }})">
                            <i data-lucide="plus" class="w-3 h-3"></i>
                            Ajouter
                        </button>
                    </div>
                </div>

                <!-- Stock Status Indicator -->
                {% if product.stock <= product.min_stock %}
                <div class="absolute top-2 right-2 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                {% elif product.stock <= product.min_stock * 2 %}
                <div class="absolute top-2 right-2 w-3 h-3 bg-yellow-500 rounded-full"></div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Right Panel - Cart -->
    <div class="cart-modern animate-slide-in-right" style="animation-delay: 0.2s;">
        <!-- Cart Header -->
        <div class="cart-header-modern">
            <h2 class="cart-title-modern">
                <i data-lucide="shopping-cart" class="w-5 h-5"></i>
                Panier
            </h2>
            <button class="quick-action-btn-modern" onclick="clearCart()" title="Vider le panier">
                <i data-lucide="trash-2" class="w-4 h-4"></i>
            </button>
        </div>

        <!-- Cart Items -->
        <div class="cart-items-modern" id="cartItems">
            <!-- Cart items will be populated here -->
            <div class="text-center py-12 text-neutral-400">
                <i data-lucide="shopping-cart" class="w-12 h-12 mx-auto mb-4 opacity-50"></i>
                <p class="text-sm">Votre panier est vide</p>
                <p class="text-xs mt-1">Sélectionnez des produits pour commencer</p>
            </div>
        </div>

        <!-- Cart Total -->
        <div class="cart-total-modern">
            <div class="space-y-3 mb-6">
                <div class="flex justify-between text-sm">
                    <span class="text-neutral-400">Sous-total</span>
                    <span id="subtotal" class="text-neutral-100 font-mono">0.00 €</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-neutral-400">TVA (20%)</span>
                    <span id="tax" class="text-neutral-100 font-mono">0.00 €</span>
                </div>
                <div class="h-px bg-gradient-to-r from-transparent via-neutral-600 to-transparent"></div>
                <div class="cart-total-amount-modern">
                    <span>Total</span>
                    <span class="amount" id="total">0.00 €</span>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
                <button type="button"
                        onclick="showPaymentModal()"
                        class="btn-primary-modern w-full py-4 text-lg font-semibold"
                        id="payButton"
                        disabled>
                    <i data-lucide="credit-card" class="w-5 h-5"></i>
                    Procéder au paiement
                </button>

                <div class="grid grid-cols-2 gap-2">
                    <button type="button"
                            onclick="saveOrder()"
                            class="btn-ghost-modern py-2 text-sm">
                        <i data-lucide="save" class="w-4 h-4"></i>
                        Sauvegarder
                    </button>
                    <button type="button"
                            onclick="printReceipt()"
                            class="btn-ghost-modern py-2 text-sm">
                        <i data-lucide="printer" class="w-4 h-4"></i>
                        Imprimer
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Payment Modal -->
<div id="paymentModal" class="payment-modal-modern">
    <div class="payment-modal-content-modern">
        <!-- Modal Header -->
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-neutral-100 flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center">
                    <i data-lucide="credit-card" class="w-5 h-5 text-white"></i>
                </div>
                Finaliser la commande
            </h3>
            <button onclick="closePaymentModal()" class="quick-action-btn-modern">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
        </div>

        <!-- Payment Form -->
        <form id="paymentForm" class="space-y-6">
            <!-- Table Number -->
            <div>
                <label class="block text-sm font-medium text-neutral-300 mb-2">
                    <i data-lucide="hash" class="w-4 h-4 inline mr-2"></i>
                    Numéro de table
                </label>
                <input type="number"
                       name="tableNumber"
                       class="input-modern"
                       placeholder="Ex: 12"
                       min="1"
                       max="999">
            </div>

            <!-- Payment Method -->
            <div>
                <label class="block text-sm font-medium text-neutral-300 mb-3">
                    <i data-lucide="wallet" class="w-4 h-4 inline mr-2"></i>
                    Méthode de paiement
                </label>
                <div class="grid grid-cols-2 gap-3">
                    <label class="payment-method-option">
                        <input type="radio" name="paymentMethod" value="cash" class="sr-only" checked>
                        <div class="payment-method-card">
                            <i data-lucide="banknote" class="w-6 h-6 mb-2"></i>
                            <span class="font-medium">Espèces</span>
                        </div>
                    </label>
                    <label class="payment-method-option">
                        <input type="radio" name="paymentMethod" value="card" class="sr-only">
                        <div class="payment-method-card">
                            <i data-lucide="credit-card" class="w-6 h-6 mb-2"></i>
                            <span class="font-medium">Carte</span>
                        </div>
                    </label>
                </div>
            </div>

            <!-- Amount Display -->
            <div class="glass-card p-4 border border-accent-500/30">
                <label class="block text-sm font-medium text-neutral-300 mb-2">Montant total</label>
                <div class="text-3xl font-bold font-mono text-accent-400" id="modalAmount">0.00 €</div>
            </div>

            <!-- Customer Info (Optional) -->
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-neutral-300 mb-2">Nom client (optionnel)</label>
                    <input type="text" name="customerName" class="input-modern" placeholder="Nom du client">
                </div>
                <div>
                    <label class="block text-sm font-medium text-neutral-300 mb-2">Téléphone (optionnel)</label>
                    <input type="tel" name="customerPhone" class="input-modern" placeholder="06 12 34 56 78">
                </div>
            </div>
        </form>

        <!-- Action Buttons -->
        <div class="flex gap-3 mt-8">
            <button type="button"
                    onclick="closePaymentModal()"
                    class="btn-ghost-modern flex-1 py-3">
                <i data-lucide="x" class="w-4 h-4"></i>
                Annuler
            </button>
            <button type="button"
                    onclick="processPayment()"
                    class="btn-primary-modern flex-1 py-3 text-lg font-semibold">
                <i data-lucide="check" class="w-4 h-4"></i>
                Confirmer le paiement
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Modern POS JavaScript -->
<script src="{{ url_for('static', filename='js/pos-modern.js') }}"></script>

<style>
/* Payment Method Options */
.payment-method-option {
    cursor: pointer;
}

.payment-method-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 1rem;
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
    text-align: center;
}

.payment-method-option input:checked + .payment-method-card {
    background: var(--gradient-primary);
    border-color: var(--primary-500);
    color: white;
    box-shadow: var(--shadow-neon);
}

.payment-method-card:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-500);
    transform: translateY(-2px);
}
</style>

<script>
// Modern Payment Modal Functions
function showPaymentModal() {
    const cartItems = document.getElementById('cartItems');
    if (!cartItems.children.length || cartItems.textContent.includes('Votre panier est vide')) {
        showNotification('Le panier est vide', 'error');
        return;
    }

    const total = document.getElementById('total').textContent;
    document.getElementById('modalAmount').textContent = total;

    const modal = document.getElementById('paymentModal');
    modal.classList.add('open');
}

function closePaymentModal() {
    const modal = document.getElementById('paymentModal');
    modal.classList.remove('open');
}

function processPayment() {
    const form = document.getElementById('paymentForm');
    const formData = new FormData(form);
    const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

    showLoading();

    fetch('/pos/checkout', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken
        },
        body: JSON.stringify({
            table_number: formData.get('tableNumber'),
            payment_method: formData.get('paymentMethod'),
            customer_name: formData.get('customerName'),
            customer_phone: formData.get('customerPhone')
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Paiement effectué avec succès !', 'success');
            closePaymentModal();
            // Clear cart and redirect
            setTimeout(() => {
                window.location.href = '/pos/sales_history';
            }, 1500);
        } else {
            showNotification(data.message || 'Erreur lors du paiement', 'error');
        }
    })
    .catch(error => {
        console.error('Payment error:', error);
        showNotification('Erreur de connexion', 'error');
    })
    .finally(() => {
        hideLoading();
    });
}

// Additional cart functions
function clearCart() {
    if (window.modernPOS) {
        window.modernPOS.clearCart();
    }
}

function saveOrder() {
    showNotification('Commande sauvegardée', 'info');
}

function printReceipt() {
    showNotification('Impression en cours...', 'info');
}


</script>
{% endblock %}