/* Styles généraux */
body {
    background-color: #f8f9fa;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

/* Styles de la barre de navigation */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

.navbar-brand {
    font-weight: bold;
}

/* Styles des cartes */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,.075);
    border: none;
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0,0,0,.125);
}

/* Styles des formulaires */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Styles des boutons */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* Styles des alertes */
.alert {
    border: none;
    border-radius: 0.25rem;
}

/* Styles des tableaux */
.table {
    background-color: #fff;
}

.table thead th {
    border-top: none;
    background-color: #f8f9fa;
}

/* Styles des menus déroulants */
.dropdown-menu {
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
    border: none;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

/* Styles pour la page de vente */
.pos-container {
    display: grid;
    grid-template-columns: 200px 1fr 300px;
    gap: 1rem;
    height: calc(100vh - 56px);
}

.numpad {
    background-color: #fff;
    padding: 1rem;
    border-radius: 0.25rem;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    padding: 1rem;
    overflow-y: auto;
}

.product-card {
    background-color: #fff;
    border-radius: 0.25rem;
    padding: 0.5rem;
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s;
}

.product-card:hover {
    transform: translateY(-2px);
}

.product-image {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
}

.cart {
    background-color: #fff;
    padding: 1rem;
    border-radius: 0.25rem;
    overflow-y: auto;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
}

.cart-total {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 2px solid #dee2e6;
    font-weight: bold;
}

/* Styles pour les graphiques */
.chart-container {
    background-color: #fff;
    padding: 1rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

/* Styles pour les rapports */
.report-filters {
    background-color: #fff;
    padding: 1rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

/* Styles pour la page de cuisine */
.kitchen-orders {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    padding: 1rem;
}

.kitchen-order-card {
    background-color: #fff;
    border-radius: 0.25rem;
    padding: 1rem;
}

/* Styles pour les tickets */
.ticket {
    background-color: #fff;
    padding: 1rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', Courier, monospace;
}

.ticket-header {
    text-align: center;
    margin-bottom: 1rem;
}

.ticket-items {
    margin-bottom: 1rem;
}

.ticket-total {
    text-align: right;
    font-weight: bold;
}

/* Styles pour les notifications */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Styles pour la page de détail du produit */
.product-detail {
    animation: fadeIn 0.3s ease-out;
}

.product-detail .card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.product-detail .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.product-detail .stat-card {
    transition: transform 0.2s ease-in-out;
}

.product-detail .stat-card:hover {
    transform: scale(1.02);
}

.product-detail .alert-card {
    transition: transform 0.2s ease-in-out;
}

.product-detail .alert-card:hover {
    transform: scale(1.02);
}

/* Styles pour les tableaux */
.table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-container table {
    min-width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.table-container th {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #f9fafb;
}

.dark .table-container th {
    background-color: #1f2937;
}

/* Styles pour les badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
    transition: all 0.2s ease-in-out;
}

.badge:hover {
    transform: scale(1.05);
}

/* Styles pour les boutons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

/* Styles pour les cartes */
.card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease-in-out;
}

.dark .card {
    background-color: #1f2937;
}

.card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Styles pour les formulaires */
.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
}

.dark .form-input {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Styles pour les icônes */
.icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
    transition: all 0.2s ease-in-out;
}

.icon:hover {
    transform: scale(1.1);
}

/* Styles pour les transitions */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

/* Styles pour les effets de survol */
.hover-scale {
    transition: transform 0.2s ease-in-out;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Styles pour les effets de focus */
.focus-ring {
    outline: none;
    transition: box-shadow 0.2s ease-in-out;
}

.focus-ring:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Styles pour les effets de pression */
.active-scale {
    transition: transform 0.1s ease-in-out;
}

.active-scale:active {
    transform: scale(0.95);
} 